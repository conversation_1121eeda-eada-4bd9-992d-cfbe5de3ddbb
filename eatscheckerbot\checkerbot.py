import discord
from discord.ext import commands
import os
from dotenv import load_dotenv
import logging
import asyncio
import sys
import os

# Add the parent directory to the path so we can import from common
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from common.bot import process_group_order
from common.config import configure_logging
import sys
from typing import Dict, List, Set, Any
from common.bot import (
    extract_group_link,
    process_cart_items,
    calculate_fees
)

# Custom embed functions for checkerbot with standard emojis
def create_locked_order_embed():
    """Create a modern embed for locked/canceled orders."""
    embed = discord.Embed(
        title="🔒 Group Order is Locked",
        description="This group order appears to be locked or canceled.",
        color=discord.Color.from_rgb(237, 66, 69)  # Discord Red
    )

    # Add a clean divider
    embed.add_field(
        name="",
        value="━━━━━━━━━━━━━━━━━━━━━━━━━",
        inline=False
    )

    # Add steps with better formatting
    embed.add_field(
        name="🔸 Step 1",
        value="Go back to your Uber Eats Cart",
        inline=False
    )

    embed.add_field(
        name="🔹 Step 2",
        value="Press **Unlock Group Order**",
        inline=False
    )

    embed.add_field(
        name="🔸 Step 3",
        value="Resend your cart link to this channel",
        inline=False
    )

    # Set a nice thumbnail
    embed.set_thumbnail(url="https://i.imgur.com/JqA9FeL.png")  # Lock icon

    # Add footer with helpful tip
    embed.set_footer(text="If you continue to have issues, please contact support")

    return embed

def create_non_promo_embed():
    """Create a modern embed for stores not in promo."""
    embed = discord.Embed(
        title="🔎 Find Eligible Stores",
        description="**The store you're looking for isn't in our promo. Follow these steps to find eligible restaurants:**",
        color=discord.Color.from_rgb(254, 231, 92)  # Bright Yellow
    )

    # Add a clean divider
    embed.add_field(
        name="",
        value="━━━━━━━━━━━━━━━━━━━━━━━━━",
        inline=False
    )

    # Step 1
    embed.add_field(
        name="🔸 Step 1: Open Our Special Link",
        value="Open [**this link**](https://tinyurl.com/TheMethodUE) in Chrome or Safari",
        inline=False
    )

    # Step 2
    embed.add_field(
        name="🔹 Step 2: Enter Your Address",
        value="Log in to Uber Eats and enter your delivery address",
        inline=False
    )

    # Step 3
    embed.add_field(
        name="🔸 Step 3: Open Link Again",
        value="Open [**the same link**](https://tinyurl.com/TheMethodUE) in a second tab",
        inline=False
    )

    # Step 4
    embed.add_field(
        name="🔹 Step 4: Browse Eligible Restaurants",
        value="You'll now see all restaurants eligible for our promo!",
        inline=False
    )

    # Set a thumbnail instead of full image for cleaner look
    embed.set_thumbnail(url="https://i.imgur.com/JNRmHjM.png")  # Search/store icon

    # Add a footer with a tip
    embed.set_footer(text="Tip: If you still don't see eligible stores, try clearing your browser cookies")

    # Add the screenshot as a smaller image at the bottom
    embed.set_image(url="https://media.discordapp.net/attachments/978162420423999498/1349998616110043237/image.png?width=600&height=338")

    return embed

def create_order_summary_embed(result, cart_items, fee_calculations):
    """Create a modern order summary embed."""
    embed = discord.Embed(
        title="🍝 Group Order Summary",
        color=discord.Color.from_rgb(87, 242, 135)  # Vibrant Green
    )

    # Add group order link if available with better formatting
    group_link = result.get('group_link', 'Not available')
    embed.description = f"**🎟️ Promo Status:** ✅ Store is in promo!\n**🔗 Group Order:** [Click to view]({group_link})\n\n"

    # Add a clean divider
    embed.add_field(
        name="",
        value="━━━━━━━━━━━━━━━━━━━━━━━━━",
        inline=False
    )

    # Location with better formatting
    location = result.get('location', {})
    location_str = f"{location.get('address', 'Unknown Address')}, {location.get('city', 'Unknown')}, {location.get('state', 'XX').upper()}"
    embed.add_field(
        name="📍 Delivery Location",
        value=f"```{location_str}```",
        inline=False
    )

    # Cart items with better formatting
    if cart_items:
        items_str = "\n".join([f"• └──{item}" for item in cart_items])
        embed.add_field(
            name="🛒 Order Items",
            value=items_str,
            inline=False
        )

    # Price breakdown with better formatting
    currency = "CA$" if fee_calculations.get('is_cad', False) else "$"

    # Original vs. discounted price
    embed.add_field(
        name="💰 Price Breakdown",
        value=f"**Original Subtotal:** `{currency}{fee_calculations['subtotal']:.2f}`\n" +
              f"**Discounted (70% OFF):** `{currency}{fee_calculations['discounted_subtotal']:.2f}`\n" +
              f"**You Save:** `{currency}{fee_calculations['savings']:.2f}`",
        inline=False
    )

    # Fees breakdown
    fees_str = f"**Delivery Fee:** `{currency}{fee_calculations['delivery_fee']:.2f}`\n"

    if fee_calculations.get('overflow_fee', 0) > 0:
        fees_str += f"**Overflow Fee:** `{currency}{fee_calculations['overflow_fee']:.2f}`\n"

    fees_str += f"**Service Fee:** `{currency}{fee_calculations['service_fee']:.2f}`\n"

    if fee_calculations.get('ca_driver_benefit', 0) > 0:
        fees_str += f"**Driver Benefit:** `{currency}{fee_calculations['ca_driver_benefit']:.2f}`\n"

    fees_str += f"**Taxes:** `{currency}{fee_calculations['taxes']:.2f}`"

    embed.add_field(
        name="💸 Fees & Taxes",
        value=fees_str,
        inline=False
    )

    # Final total with emphasis
    embed.add_field(
        name="💲 Final Total",
        value=f"**`{currency}{fee_calculations['final_total']:.2f}`**",
        inline=False
    )

    # Set a nice thumbnail
    embed.set_thumbnail(url="https://i.imgur.com/GwSvqWD.png")  # Food/order icon

    # Add footer with helpful information
    embed.set_footer(text="A Chef or Waiter will assist you shortly | The Method")

    return embed

def check_order_limits(subtotal, is_cad):
    """Check if order meets minimum/maximum requirements with modern embed."""
    currency = "CA$" if is_cad else "$"
    min_order = 30.0 if is_cad else 24.0
    max_order = 35.0 if is_cad else 30.0

    if subtotal < min_order:
        embed = discord.Embed(
            title="⚠️ Minimum Order Requirement",
            description=f"Your subtotal must be at least `{currency}{min_order:.2f}` before fees and taxes.",
            color=discord.Color.from_rgb(254, 231, 92)  # Bright Yellow
        )

        # Add a clean divider
        embed.add_field(
            name="",
            value="━━━━━━━━━━━━━━━━━━━━━━━━━",
            inline=False
        )

        # Add action required
        embed.add_field(
            name="👉 Action Required",
            value="Please add more items to your cart to meet the minimum requirement.",
            inline=False
        )

        # Add current vs required
        embed.add_field(
            name="📊 Current vs Required",
            value=f"**Current Subtotal:** `{currency}{subtotal:.2f}`\n" +
                  f"**Minimum Required:** `{currency}{min_order:.2f}`\n" +
                  f"**Amount to Add:** `{currency}{(min_order - subtotal):.2f}`",
            inline=False
        )

        # Set a nice thumbnail
        embed.set_thumbnail(url="https://i.imgur.com/YKI4LbC.png")  # Warning icon

        return embed

    elif subtotal > max_order:
        embed = discord.Embed(
            title="⚠️ Maximum Order Limit Exceeded",
            description=f"Your subtotal should be `{currency}{max_order:.2f}` or less before fees and taxes.",
            color=discord.Color.from_rgb(254, 231, 92)  # Bright Yellow
        )

        # Add a clean divider
        embed.add_field(
            name="",
            value="━━━━━━━━━━━━━━━━━━━━━━━━━",
            inline=False
        )

        # Add action required
        embed.add_field(
            name="👉 Action Required",
            value="Please remove some items from your cart to meet the maximum limit.",
            inline=False
        )

        # Add current vs required
        embed.add_field(
            name="📊 Current vs Limit",
            value=f"**Current Subtotal:** `{currency}{subtotal:.2f}`\n" +
                  f"**Maximum Allowed:** `{currency}{max_order:.2f}`\n" +
                  f"**Amount to Remove:** `{currency}{(subtotal - max_order):.2f}`",
            inline=False
        )

        # Set a nice thumbnail
        embed.set_thumbnail(url="https://i.imgur.com/YKI4LbC.png")  # Warning icon

        return embed

    return None

# Load environment variables
load_dotenv()
CHECKER_TOKEN = os.getenv('checker_token')

# Configure logging
configure_logging()

logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        # Terminal handler - only show INFO and above
        logging.StreamHandler(sys.stdout),
        # File handler - show all DEBUG and above
        logging.FileHandler('debug.log', encoding='utf-8', mode='w')
    ]
)

# Configure terminal handler to be less verbose
logging.getLogger().handlers[0].setLevel(logging.INFO)  # Terminal shows INFO and above
logging.getLogger().handlers[1].setLevel(logging.DEBUG)  # File shows DEBUG and above

# Create logger
logger = logging.getLogger('check_group_order')
logger.setLevel(logging.DEBUG)
configure_logging(debug_mode=True)

# Test logging
logger.debug("Logging initialized in check_group_order.py")

# Validate token
if not CHECKER_TOKEN:
    logger.error("checker_token not found in .env file")
    exit(1)

# Global state to track processing links
class OrderProcessor:
    def __init__(self):
        # Track links being processed
        self.processing_links: Set[str] = set()
        # Track links that have been processed
        self.processed_links: Dict[str, Dict[str, Any]] = {}
        # Queue for links waiting to be processed
        self.link_queue: asyncio.Queue = asyncio.Queue()
        # Track channels where links were posted
        self.link_channels: Dict[str, discord.TextChannel] = {}
        # Track messages that triggered processing
        self.link_messages: Dict[str, discord.Message] = {}
        # Maximum concurrent processing
        self.max_concurrent = 5
        # Track active tasks
        self.active_tasks: List[asyncio.Task] = []
        # Semaphore to limit concurrent API calls
        self.semaphore = asyncio.Semaphore(self.max_concurrent)
        # Session pool for reusing HTTP sessions
        self.session_pool = None
        # Lock for thread-safe operations
        self.lock = asyncio.Lock()

    # This method is no longer used - all functionality moved to on_message handler
    # Keeping it for backward compatibility
    async def add_link(self, link: str, channel: discord.TextChannel, message: discord.Message) -> bool:
        """DEPRECATED: Add a link to the processing queue. Returns True if added, False if already being processed"""
        logger.warning(f"Deprecated add_link method called for link: {link}")
        return False

    async def process_queue(self) -> None:
        """Process links from the queue"""
        try:
            while not self.link_queue.empty():
                # Get next link from queue
                link = await self.link_queue.get()

                # Use a lock to check if already processed
                async with self.lock:
                    # Allow processing the same link multiple times
                    # Just log that we've seen this link before
                    if link in self.processed_links:
                        logger.info(f"Link already processed before, processing again: {link}")

                    # Double-check that it's still in processing_links
                    if link not in self.processing_links:
                        logger.warning(f"Link {link} was in queue but not in processing_links. Adding it back.")
                        self.processing_links.add(link)

                # Get channel and message
                channel = self.link_channels.get(link)
                message = self.link_messages.get(link)

                if not channel or not message:
                    logger.error(f"Missing channel or message for link: {link}")
                    async with self.lock:
                        if link in self.processing_links:
                            self.processing_links.remove(link)
                    self.link_queue.task_done()
                    continue

                # Process the link with semaphore to limit concurrent API calls
                async with self.semaphore:
                    try:
                        logger.info(f"Processing link from queue: {link}")
                        await self.process_link(link, channel, message)
                    except Exception as e:
                        logger.error(f"Error processing link {link}: {str(e)}")
                        await channel.send(f"❌ Error processing group order: {str(e)}")
                    finally:
                        # Mark as done
                        self.link_queue.task_done()
        except Exception as e:
            logger.error(f"Error in process_queue: {str(e)}")
        finally:
            # Remove this task from active tasks
            for i, task in enumerate(self.active_tasks):
                if task == asyncio.current_task():
                    self.active_tasks.pop(i)
                    break

    async def process_link(self, link: str, channel: discord.TextChannel, _message: discord.Message) -> None:
        """Process a single group order link"""
        try:
            # Notify processing with a more informative message
            status_msg = await channel.send(f"🔍 **Processing your group order link**\n> Please wait while we analyze your order...")

            # Process the group order
            result = await process_group_order(link)

            if not result:
                await status_msg.edit(content="❌ **Failed to process group order**\n> The link may be invalid or expired. Please try again with a new link.")
                return

            # Add the group link to the result dictionary
            if isinstance(result, dict):
                result['group_link'] = link

            # Handle locked/error cases
            if isinstance(result, dict) and 'error' in result:
                if result['error'].get('type') == 'LOCKED_ORDER':
                    await status_msg.edit(content="🔒 **Group order is locked or canceled**\n> Please unlock your group order and try again.")
                    await channel.send(embed=create_locked_order_embed())
                    return

            # This check is redundant since we already checked above, but keeping for safety
            if not result:
                await status_msg.edit(content="🔒 **Group order is locked or canceled**\n> Please unlock your group order and try again.")
                await channel.send(embed=create_locked_order_embed())
                return

            # Handle non-promo stores
            is_promo = result.get('is_promo', False)
            logger.info(f"Promo status for link {link}: {is_promo}")
            if not is_promo:
                await status_msg.edit(content="⚠️ **Store is not in our promo**\n> Please check the instructions below to find eligible restaurants.")
                await channel.send(embed=create_non_promo_embed())
                # Store the processed result even if not in promo
                self.processed_links[link] = result
                return

            # Process cart items
            cart_items, _ = process_cart_items(result.get('cart_items', []))

            # Use the fees directly from the API response
            fees_data = result.get('fees', {})
            if fees_data:
                subtotal = fees_data['subtotal']
                # Check if the subtotal contains 'CA' to determine if it's CAD
                is_cad = isinstance(subtotal, str) and 'CA' in subtotal
                # Clean up subtotal if it's a string
                if isinstance(subtotal, str):
                    subtotal = float(subtotal.replace('CA', '').replace('$', '').strip())

                # Calculate overflow fee
                threshold = 30 if is_cad else 25
                overflow_fee = round(max(0, subtotal - threshold), 2)

                # Calculate discounted subtotal (30% of original)
                discounted_subtotal = round(subtotal * 0.3, 2)

                fee_calculations = {
                    'subtotal': subtotal,
                    'discounted_subtotal': discounted_subtotal,
                    'savings': round(subtotal * 0.7, 2),
                    'overflow_fee': overflow_fee,
                    'service_fee': fees_data['service_fee'],
                    'delivery_fee': fees_data['delivery_fee'],
                    'ca_driver_benefit': fees_data['ca_driver_benefit'],
                    'taxes': fees_data['taxes'],
                    'total_fees': fees_data['total_fees_before_discount'],
                    'uber_one_discount': fees_data['uber_one_discount'],
                    'final_fees': fees_data['total_fees'],
                    'final_total': round(discounted_subtotal + overflow_fee + fees_data['total_fees'], 2)
                }
            else:
                # Fallback if no fees data is available
                subtotal = 0
                is_cad = False
                fee_calculations = calculate_fees({}, 0, False)

            # Create and send order summary
            summary_embed = create_order_summary_embed(result, cart_items, fee_calculations)
            await status_msg.delete()
            await channel.send(embed=summary_embed)

            # Check and send order limits warning if needed
            limit_warning = check_order_limits(subtotal, is_cad)
            if limit_warning:
                await channel.send(embed=limit_warning)
            else:
                await channel.send("## 👋 Hello! Your order has been processed successfully\n> Please wait patiently, a Chef or Waiter will assist you shortly. Thank you!")

            # Store the processed result with lock to ensure thread-safety
            async with self.lock:
                self.processed_links[link] = result
                logger.info(f"Link processed and added to processed_links: {link}")

        except Exception as e:
            logger.error(f"Error processing link {link}: {str(e)}")
            await channel.send(f"❌ **Error processing group order**\n> {str(e)}\n> Please try again or contact support if the issue persists.")
        finally:
            # Remove from processing set with lock
            async with self.lock:
                if link in self.processing_links:
                    self.processing_links.remove(link)
                    logger.info(f"Removed link from processing_links: {link}")

# Initialize the order processor
order_processor = OrderProcessor()

# Set up Discord bot
intents = discord.Intents.default()
intents.message_content = True
bot = commands.Bot(command_prefix="!", intents=intents)

async def setup_background_tasks():
    """Start background tasks when bot starts"""
    # Start the queue processor
    for _ in range(order_processor.max_concurrent):
        task = asyncio.create_task(order_processor.process_queue())
        order_processor.active_tasks.append(task)

@bot.event
async def on_ready():
    logger.info(f"Bot is ready! Logged in as {bot.user}")

@bot.event
async def setup_hook():
    # This is called before the bot starts running
    bot.loop.create_task(setup_background_tasks())

@bot.command(name="status")
async def status(ctx):
    """Show status of all processing links"""
    processing_count = len(order_processor.processing_links)
    queue_size = order_processor.link_queue.qsize()
    processed_count = len(order_processor.processed_links)

    # Create a modern status embed with better visual design
    status_embed = discord.Embed(
        title="📊 Order Processing Dashboard",
        description="Real-time status of group order processing system",
        color=discord.Color.from_rgb(88, 101, 242)  # Discord Blurple
    )

    # Add a clean divider
    status_embed.add_field(
        name="",
        value="━━━━━━━━━━━━━━━━━━━━━━━━━",
        inline=False
    )

    # Add system metrics with better formatting
    status_embed.add_field(
        name="📈 System Metrics",
        value=f"**🔄 Processing:** `{processing_count}` active links\n"
              f"**⏳ Queue:** `{queue_size}` waiting links\n"
              f"**✅ Completed:** `{processed_count}` processed links",
        inline=False
    )

    # Add a visual indicator of system load
    load_percentage = min(100, int((processing_count / max(1, order_processor.max_concurrent)) * 100))
    load_bars = int(load_percentage / 10)
    load_visual = "\u25fc" * load_bars + "\u25fb" * (10 - load_bars)

    status_embed.add_field(
        name="📊 System Load",
        value=f"`{load_visual}` {load_percentage}%\n" +
              f"*{order_processor.max_concurrent - processing_count} of {order_processor.max_concurrent} slots available*",
        inline=False
    )

    # Add currently processing links with better formatting
    if processing_count > 0:
        processing_links = "\n".join([f"`{i+1}.` [{link[:25]}...]({link})" for i, link in enumerate(order_processor.processing_links)])
        status_embed.add_field(
            name="🔄 Currently Processing",
            value=processing_links or "*None*",
            inline=False
        )

    # Set a nice thumbnail and footer
    status_embed.set_footer(text=f"Last updated: {discord.utils.utcnow().strftime('%H:%M:%S UTC')} | The Method")

    await ctx.send(embed=status_embed)

@bot.command(name="clear")
async def clear_queue(ctx):
    """Clear the processing queue (admin only)"""
    # Check if user has admin permissions
    if not ctx.author.guild_permissions.administrator:
        await ctx.send("⚠️ **Permission Denied**\n> You need administrator permissions to use this command.")
        return

    # Clear the queue
    while not order_processor.link_queue.empty():
        try:
            order_processor.link_queue.get_nowait()
            order_processor.link_queue.task_done()
        except asyncio.QueueEmpty:
            break

    # Clear processing links
    order_processor.processing_links.clear()

    await ctx.send("✅ **Success!**\n> Processing queue has been cleared.")

@bot.event
async def on_message(message):
    # Process commands first
    await bot.process_commands(message)

    # Skip messages from the bot itself
    if message.author == bot.user:
        return

    try:
        # Extract group link
        group_link = await extract_group_link(message)
        if not group_link:
            return

        # Normalize the link to ensure consistent matching
        # Remove any trailing parameters or fragments
        if '?' in group_link:
            group_link = group_link.split('?')[0]
        if '#' in group_link:
            group_link = group_link.split('#')[0]

        # Ensure the link ends with /join for consistency
        if not group_link.endswith('/join'):
            if group_link.endswith('/'):
                group_link = group_link + 'join'
            else:
                group_link = group_link + '/join'

        # Check if this link is already being processed or has been processed
        async with order_processor.lock:
            if group_link in order_processor.processing_links:
                logger.info(f"Link already being processed, skipping: {group_link}")
                await message.channel.send(f"⚠️ **This group order link is already being processed**\n> Please wait for the current processing to complete.")
                return

            # Allow processing the same link multiple times
            # Just log that we've seen this link before
            if group_link in order_processor.processed_links:
                logger.info(f"Link already processed before, processing again: {group_link}")

            # Mark as processing immediately to prevent duplicate processing
            order_processor.processing_links.add(group_link)
            logger.info(f"Marked link as processing: {group_link}")

        # Store channel and message info
        order_processor.link_channels[group_link] = message.channel
        order_processor.link_messages[group_link] = message

        # Add to queue
        await order_processor.link_queue.put(group_link)
        logger.info(f"Added link to queue: {group_link}")

        # Start processing if needed
        if len(order_processor.active_tasks) < order_processor.max_concurrent:
            task = asyncio.create_task(order_processor.process_queue())
            order_processor.active_tasks.append(task)
            logger.info(f"Started new queue processor task")

        # Notify the user

    except Exception as e:
        logger.error(f"Error in on_message: {str(e)}")
        await message.channel.send(f"❌ **Error**\n> {str(e)}\n> Please try again or contact support if the issue persists.")

def run_bot():
    # Run the bot
    bot.run(CHECKER_TOKEN)

if __name__ == "__main__":
    run_bot()