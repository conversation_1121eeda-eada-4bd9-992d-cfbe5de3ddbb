import sys
import os
import asyncio
import logging
import argparse
import importlib
import subprocess

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('main.log', encoding='utf-8')
    ]
)

logger = logging.getLogger('main')

def run_bot(bot_name):
    """Run a specific bot by name."""
    try:
        if bot_name == "themethodbot":
            from themethodbot import themethodbot
            themethodbot.run_bot()
        elif bot_name == "budgetbot":
            from budgetbot import budgetbot
            # budgetbot doesn't have a run_bot function, so we call bot.run directly
            budgetbot.bot.run(budgetbot.DISCORD_BOT_TOKEN, log_handler=None)
        elif bot_name == "eatscheckerbot":
            from eatscheckerbot import eatscheckerbot
            # eatscheckerbot doesn't have a run_bot function, so we call bot.run directly
            eatscheckerbot.bot.run(eatscheckerbot.DISCORD_BOT_TOKEN)
        else:
            logger.error(f"Unknown bot: {bot_name}")
            print(f"Unknown bot: {bot_name}")
            print("Available bots: themethodbot, budgetbot, eatscheckerbot")
    except Exception as e:
        logger.error(f"Error running {bot_name}: {e}")
        import traceback
        logger.error(traceback.format_exc())

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Run a Discord bot')
    parser.add_argument('bot', choices=['themethodbot', 'budgetbot', 'eatscheckerbot', 'all'],
                        help='The bot to run')

    args = parser.parse_args()

    if args.bot == 'all':
        logger.info("Running all bots simultaneously...")
        try:
            # Import and run the run_all_bots module
            import run_all_bots
            run_all_bots.run_all_bots()
        except KeyboardInterrupt:
            logger.info("Bots stopped by keyboard interrupt")
        except Exception as e:
            logger.error(f"Error running all bots: {e}")
            import traceback
            logger.error(traceback.format_exc())
    else:
        run_bot(args.bot)
