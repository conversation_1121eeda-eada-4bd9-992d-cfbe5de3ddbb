2025-04-05 13:38:54,425 - main - INFO - Running all bots simultaneously...
2025-04-05 13:38:54,445 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-05 13:38:54,495 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-05 13:38:54,501 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-05 13:38:56,034 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-05 13:38:56,041 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-05 13:38:56,048 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-05 13:38:56,143 - run_all_bots - INFO - All bots have been shut down.
2025-04-05 13:42:44,253 - main - INFO - Running all bots simultaneously...
2025-04-05 13:42:44,269 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-05 13:42:44,277 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-05 13:42:44,282 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-05 13:42:45,618 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-05 13:42:45,620 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-05 13:42:45,629 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-05 13:42:45,723 - run_all_bots - INFO - All bots have been shut down.
2025-04-05 13:45:23,684 - main - INFO - Running all bots simultaneously...
2025-04-05 13:45:23,691 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-05 13:45:23,698 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-05 13:45:23,703 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-05 13:45:25,111 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-05 13:45:25,153 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-05 13:45:25,182 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-07 09:57:56,822 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-07 11:36:20,950 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-07 11:40:48,319 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-10 02:56:25,634 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-10 02:56:25,639 - run_all_bots - INFO - All bots have been shut down.
2025-04-10 02:56:40,153 - main - INFO - Running all bots simultaneously...
2025-04-10 02:56:40,162 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-10 02:56:40,169 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-10 02:56:40,174 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-10 02:56:41,603 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-10 02:56:41,628 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-10 02:56:41,649 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-10 03:04:15,712 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-10 03:04:15,712 - run_all_bots - INFO - All bots have been shut down.
2025-04-10 03:04:22,159 - main - INFO - Running all bots simultaneously...
2025-04-10 03:04:22,166 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-10 03:04:22,175 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-10 03:04:22,179 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-10 03:04:23,475 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-10 03:04:23,501 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-10 03:04:23,527 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-10 03:09:39,607 - main - INFO - Running all bots simultaneously...
2025-04-10 03:09:39,615 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-10 03:09:39,622 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-10 03:09:39,626 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-10 03:09:41,103 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-10 03:09:41,112 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-10 03:09:41,132 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-10 03:13:42,644 - main - INFO - Running all bots simultaneously...
2025-04-10 03:13:42,652 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-10 03:13:42,660 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-10 03:13:42,666 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-10 03:13:43,981 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-10 03:13:43,982 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-10 03:13:43,990 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-10 03:18:32,961 - main - INFO - Running all bots simultaneously...
2025-04-10 03:18:32,970 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-10 03:18:32,979 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-10 03:18:32,988 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-10 03:18:34,689 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-10 03:18:34,690 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-10 03:18:34,704 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-10 03:19:48,177 - main - INFO - Running all bots simultaneously...
2025-04-10 03:19:48,185 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-10 03:19:48,195 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-10 03:19:48,201 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-10 03:19:49,506 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-10 03:19:49,540 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-10 03:19:49,551 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-10 08:28:02,978 - run_all_bots - INFO - All bots have been shut down.
2025-04-10 08:32:49,600 - main - INFO - Running all bots simultaneously...
2025-04-10 08:32:49,609 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-10 08:32:49,617 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-10 08:32:49,624 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-10 08:32:51,662 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-10 08:32:51,674 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-10 08:32:51,677 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-10 10:39:56,229 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-10 10:39:56,229 - run_all_bots - INFO - All bots have been shut down.
2025-04-10 12:04:32,351 - main - INFO - Running all bots simultaneously...
2025-04-10 12:04:32,360 - run_all_bots - INFO - Starting process for themethodbot...
2025-04-10 12:04:32,368 - run_all_bots - INFO - Starting process for budgetbot...
2025-04-10 12:04:32,374 - run_all_bots - INFO - Starting process for eatscheckerbot...
2025-04-10 12:04:33,759 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-10 12:04:33,826 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-10 12:04:33,830 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-04-10 12:14:53,277 - run_all_bots - INFO - Received keyboard interrupt, shutting down all bots...
2025-04-10 12:14:53,277 - run_all_bots - INFO - Terminating eatscheckerbot...
2025-04-10 12:14:53,280 - run_all_bots - INFO - All bots have been shut down.
2025-06-03 13:23:33,168 - check_group_order - DEBUG - Logging initialized in check_group_order.py
2025-07-08 20:06:55,845 - check_group_order - DEBUG - Logging initialized in check_group_order.py
