import multiprocessing
import logging
import sys
import os
import traceback

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('all_bots.log', encoding='utf-8')
    ]
)

logger = logging.getLogger('run_all_bots')

def run_themethodbot():
    """Run The Method Bot in a separate process."""
    try:
        from themethodbot import themethodbot
        logger.info("Starting themethodbot...")
        themethodbot.run_bot()
    except Exception as e:
        logger.error(f"Error running themethodbot: {e}")
        logger.error(traceback.format_exc())

def run_budgetbot():
    """Run Budget Bot in a separate process."""
    try:
        from budgetbot import budgetbot
        logger.info("Starting budgetbot...")
        # budgetbot doesn't have a run_bot function, so we call bot.run directly
        budgetbot.bot.run(budgetbot.DISCORD_BOT_TOKEN, log_handler=None)
    except Exception as e:
        logger.error(f"Error running budgetbot: {e}")
        logger.error(traceback.format_exc())

def run_eatscheckerbot():
    """Run Eats Checker Bot in a separate process."""
    try:
        from eatscheckerbot import eatscheckerbot
        logger.info("Starting eatscheckerbot...")
        # eatscheckerbot doesn't have a run_bot function, so we call bot.run directly
        eatscheckerbot.bot.run(eatscheckerbot.DISCORD_BOT_TOKEN)
    except Exception as e:
        logger.error(f"Error running eatscheckerbot: {e}")
        logger.error(traceback.format_exc())

def run_all_bots():
    """Run all bots concurrently using multiprocessing."""
    # Create processes for each bot
    processes = [
        multiprocessing.Process(target=run_themethodbot, name="themethodbot"),
        multiprocessing.Process(target=run_budgetbot, name="budgetbot"),
        multiprocessing.Process(target=run_eatscheckerbot, name="eatscheckerbot")
    ]

    # Start all processes
    for process in processes:
        logger.info(f"Starting process for {process.name}...")
        process.start()

    try:
        # Wait for all processes to complete (which they won't unless terminated)
        for process in processes:
            process.join()
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt, shutting down all bots...")
        # Terminate all processes on keyboard interrupt
        for process in processes:
            if process.is_alive():
                logger.info(f"Terminating {process.name}...")
                process.terminate()

        # Wait for processes to terminate
        for process in processes:
            process.join(timeout=5)
            if process.is_alive():
                logger.warning(f"{process.name} did not terminate gracefully, killing...")
                process.kill()

    logger.info("All bots have been shut down.")

if __name__ == "__main__":
    run_all_bots()
