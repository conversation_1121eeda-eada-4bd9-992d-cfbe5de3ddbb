import discord
from discord.ext import commands
from discord import app_commands
import logging
import os
from dotenv import load_dotenv
import re
import asyncio
import traceback
import aiohttp
import ssl
import time
import psutil
from typing import Dict, Optional, Any, List



import sys
import os

# Add the parent directory to the path so we can import from common
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from common.bot import (
    fetch_order_details,
    latestsummary,
    process_group_order,
    selectedstores,
    extract_group_link,
    create_locked_order_embed,
    process_cart_items,
    calculate_fees,
    create_order_summary_embed,
    check_order_limits,
    UBER_COOKIE
)

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('budgetbot.log', encoding='utf-8')
    ]
)

# Create a logger for this module
logger = logging.getLogger('budgetbot')

# Get environment variables
DISCORD_BOT_TOKEN = os.getenv("BUDGET_DISCORD_BOT_TOKEN")
DISCORD_GUILD_ID = int(os.getenv("BUDGET_DISCORD_GUILD_ID"))
CHECKER_TOKEN = os.getenv('checker_token')
COMMANDS_GLOBAL = False

# Set up bot
intents = discord.Intents.default()
intents.messages = True
intents.guilds = True
intents.message_content = True
intents.dm_messages = True

# Create the bot with optimized settings
bot = commands.Bot(
    command_prefix="!",
    intents=intents,
    dm_permission=True,
    case_insensitive=True,  # Make commands case-insensitive
    max_messages=10000,     # Increase message cache for better performance
    heartbeat_timeout=150.0 # Increase heartbeat timeout for stability
)

GUILD = discord.Object(id=DISCORD_GUILD_ID)

# Global session for HTTP requests
_http_session: Optional[aiohttp.ClientSession] = None

# Performance metrics
command_metrics: Dict[str, Dict[str, float]] = {}

# Cache for frequently accessed data
data_cache: Dict[str, Any] = {}

# Active tracking tasks
active_tracking_tasks: List[asyncio.Task] = []

# Command queue for cooldown commands
command_queue: Dict[str, List[Dict[str, Any]]] = {}

# Flag to track if the queue processor is running
queue_processor_running = False

class OrderTrackButton(discord.ui.View):
    def __init__(self, order_link: str):
        super().__init__()

        # Create a blue (primary) style button
        track_button = discord.ui.Button(
            label="🔍 Track Order",
            style=discord.ButtonStyle.primary,  # This makes it blue
            url=order_link
        )
        self.add_item(track_button)

class PaymentMethodButtons(discord.ui.View):
    def __init__(self):
        super().__init__(timeout=None)

    @discord.ui.button(label="💳 Payments for Shams", style=discord.ButtonStyle.primary)
    async def payment_button(self, interaction: discord.Interaction, _: discord.ui.Button):
        embed = discord.Embed(
            title="💰 Payment Details",
            color=discord.Color.blue()
        )
        embed.add_field(name="💵 CashApp/Applepay/Googlepay/Card", value="https://buy.stripe.com/14k02t418gEwbpS6oo", inline=False)
        embed.set_thumbnail(url="https://images-ext-1.discordapp.net/external/bRiKpP4Tw6Q1jqWW7nNDCU5y4zWcfyEeo0ooW5uFxz4/https/i.gyazo.com/e1abb32a09bb93d21e7e342cfdc44a1e.gif?width=72&height=72")

        await interaction.response.send_message(embed=embed)

def create_non_promo_embed():
    """Create embed for stores not in promo."""
    embed = discord.Embed(
        title="⚠️ Store Not in Promo",
        description="Follow these steps to find eligible stores:",
        color=discord.Color.yellow()
    )

    embed.set_image(url="https://media.discordapp.net/attachments/978162420423999498/1349998616110043237/image.png?ex=67d86f6f&is=67d71def&hm=43a18ef135008ca5afa451b11e762db1f2653cb163051d25e7194a16e87305a7&=&format=webp&quality=lossless")

    embed.add_field(
        name="Steps to Find Eligible Stores",
        value=(
            "## PLEAE DO NOT USE DISCORD BROWSER\n"
            "1. **Open this link in your Chrome/Safari Browser:**\n"
            "[Eligible Restaurants](http://tiny.cc/52qc001)\n"
            "\n"
            "2. **Enter Your Address**\n"
            "The link will take you to Uber Eats. Log in and Input your address and proceed.\n"
            "\n"
            "3. **Open the Link in your Chrome/Safari Browser again**\n"
            "[Eligible Restaurants](http://tiny.cc/52qc001)\n"
            "\n"
            "4. **Done!**\n"
            "You'll now see all eligible restaurants near you."
        ),
        inline=False
    )
    return embed

def format_cart_items(raw_cart_items):
    """Helper function to format cart items with customizations"""
    formatted_items = []
    for item in raw_cart_items:
        base_price = float(item.get('price', 0))
        quantity = int(item.get('quantity', 1))
        title = item.get('title', 'Unknown Item')

        # Process customizations
        customizations = item.get('customizations', {})
        addon_price = 0
        addon_names = []

        # Iterate through all customization groups
        for group_key, customization_list in customizations.items():
            for customization in customization_list:
                price = float(customization.get('price', 0))
                if price > 0:  # Only include paid add-ons
                    addon_price += price
                    addon_names.append(customization.get('title', ''))

        # Calculate total item price (base + addons) in dollars
        total_item_price = (base_price + addon_price) / 100

        # Format the item string with addons if present
        item_text = title
        if addon_names:
            paid_addons = [name for name in addon_names if name]
            if paid_addons:
                item_text += f" ({', '.join(paid_addons)})"

        formatted_items.append(f"╰・x{quantity} {item_text} (${total_item_price:.2f})")

    return formatted_items

async def track_order_status(order_id: str, channel, session: Optional[aiohttp.ClientSession] = None):
    """Tracks order status and sends updates via embeds."""
    try:
        order_id_match = re.search(r"orders/([a-f0-9-]+)", order_id)
        if order_id_match:
            order_id = order_id_match.group(1)

        url = "https://www.ubereats.com/_p/api/getActiveOrdersV1"
        last_status = None
        tracking = True

        # Use provided session or get a new one
        if session is None:
            session = await get_session()

        # Create SSL context
        ssl_context = ssl.create_default_context()
        ssl_context.check_hostname = False
        ssl_context.verify_mode = ssl.CERT_NONE

        # Track this task
        if hasattr(bot, 'tracking_tasks'):
            bot.tracking_tasks.append(asyncio.current_task())
            while tracking:
                try:
                    headers = {
                        "accept": "*/*",
                        "content-type": "application/json",
                        "x-csrf-token": "x",
                        "cookie": UBER_COOKIE if UBER_COOKIE else "",
                        "origin": "https://www.ubereats.com",
                        "referer": f"https://www.ubereats.com/orders/{order_id}"
                    }

                    payload = {
                        "orderUuid": order_id,
                        "timezone": "America/New_York"
                    }

                    # Add retry logic
                    for retry in range(3):
                        try:
                            async with session.post(url, json=payload, headers=headers, ssl=ssl_context) as response:
                                if response.status == 200:
                                    data = await response.json()

                                    if not data.get('data', {}).get('orders'):
                                        await channel.send(content="❌ Order not found")
                                        return

                                    order = data['data']['orders'][0]
                                    analytics = order.get('analytics', {}).get('data', {})
                                    order_status = analytics.get('order_status')
                                    order_phase = order.get('orderInfo', {}).get('orderPhase')

                                    if order_status != last_status or (order_phase == "COMPLETED" and tracking):
                                        last_status = order_status

                                        embed = discord.Embed(
                                            title="🚗 Order Status Update",
                                            color=discord.Color.blue()
                                        )

                                        if order_phase == "COMPLETED":
                                            embed.description = "✅ Order Complete! Enjoy your meal!"
                                            embed.color = discord.Color.purple()
                                            tracking = False

                                            await channel.send(embed=embed)

                                            # Rename the channel
                                            try:
                                                current_channel = channel
                                                ticket_number = current_channel.name.split("-")[0]
                                                new_name = f"{ticket_number}-delivered"

                                                await current_channel.edit(name=new_name)
                                                logging.info(f"✅ Successfully renamed channel to '{new_name}'")
                                            except Exception as e:
                                                logging.error(f"❌ Failed to rename channel: {str(e)}")

                                            break

                                        elif order_status == "EnrouteToRestaurant":
                                            embed.description = "Driver is heading to the Store 🚗"
                                            embed.set_thumbnail(url="https://images-ext-1.discordapp.net/external/LkgLyEJqa5wnlm7K8SXj62nNOUoCFR0gkNbqmDIAv38/https/i.gyazo.com/52098aedb69916d2b79168c5b90725c6.gif?width=72&height=72")
                                        elif order_status == "AtRestaurant":
                                            embed.description = "Driver has arrived at the Store 🏪"
                                            embed.set_thumbnail(url="https://images-ext-1.discordapp.net/external/RpwAYxrSG5sPWyqfn6ATu2TNjZmN6gkIkHtFFZZjTbM/https/i.gyazo.com/ee7b0b676c0801ccf07414cc4ab8e42a.gif?width=72&height=72")
                                        elif order_status == "FindingNewCourier":
                                            embed.description = "Finding a new driver 🚗"
                                            embed.set_thumbnail(url="https://images-ext-1.discordapp.net/external/jvNBD8wrP7OzMoJqJaZ8e_V3bDyVvGy3djjZdtBynEc/https/i.gyazo.com/fae7779eb22aaf6a0284d927bfdea74b.gif?width=72&height=72")
                                        elif order_status == "OrderPlaced":
                                            embed.description = "Order has been placed with the Store 📝"
                                            embed.set_thumbnail(url="https://images-ext-1.discordapp.net/external/I7dVXtanMLs7mQcko43gu-a7sk3Xs_uapvPvZubbAo8/https/i.gyazo.com/5ec4b7416f8329666b073f424a420949.png")
                                        elif order_status == "Preparing":
                                            embed.description = "Store is preparing your order 👨‍🍳"
                                            embed.set_thumbnail(url="https://i.imgur.com/SAuvaKY.gif")
                                        elif order_status == "ReadyForPickup":
                                            embed.description = "Order is ready for pickup 📦"
                                            embed.set_thumbnail(url="https://images-ext-1.discordapp.net/external/LkgLyEJqa5wnlm7K8SXj62nNOUoCFR0gkNbqmDIAv38/https/i.gyazo.com/52098aedb69916d2b79168c5b90725c6.gif?width=72&height=72")
                                        elif order_status == "PickedUp":
                                            embed.description = "Driver has picked up your order 🚗"
                                            embed.set_thumbnail(url="https://images-ext-1.discordapp.net/external/LkgLyEJqa5wnlm7K8SXj62nNOUoCFR0gkNbqmDIAv38/https/i.gyazo.com/52098aedb69916d2b79168c5b90725c6.gif?width=72&height=72")
                                        elif order_status == "EnrouteToDropoff" or order_status == "EnrouteToEater":
                                            eta_text = None
                                            minutes_away = None
                                            embed.set_thumbnail(url="https://images-ext-1.discordapp.net/external/LkgLyEJqa5wnlm7K8SXj62nNOUoCFR0gkNbqmDIAv38/https/i.gyazo.com/52098aedb69916d2b79168c5b90725c6.gif?width=72&height=72")

                                            background_cards = order.get('backgroundFeedCards', [])
                                            logging.info("Checking for ETA updates...")

                                            for card in background_cards:
                                                if card.get('type') == 'mapEntity':
                                                    for entity in card.get('mapEntity', []):
                                                        if entity.get('type') == 'LABEL':
                                                            title = entity.get('title', '')
                                                            subtitle = entity.get('subtitle', [])

                                                            try:
                                                                minutes_away = int(title)
                                                                subtitle_text = ' '.join(subtitle) if subtitle else ''
                                                                eta_text = f"{minutes_away} {subtitle_text}"
                                                                embed.description = f"Driver is on the way to you 🚚\nEstimated arrival: {eta_text}"
                                                            except (ValueError, TypeError) as e:
                                                                logging.error(f"Failed to parse minutes: {e}")
                                                            break

                                            if not eta_text:
                                                embed.description = "Driver is on the way to you 🚚"

                                        elif order_status in ["TimeToMeetCourier", "ArrivedAtDropoff"]:
                                            embed.description = "The driver is about to drop off your order, be ready to pick it up"
                                            embed.color = discord.Color.gold()
                                            embed.set_thumbnail(url="https://images-ext-1.discordapp.net/external/1pG3uOqsTvi3SZB5bcTG4I32e9bFkiYk9MpfLv45oBg/https/i.gyazo.com/a92427656e0c609ad54bb3c064ec099f.gif?width=72&height=72")
                                        else:
                                            embed.description = f"Current Status: {order_status}"

                                        await channel.send(embed=embed)
                                    break
                                elif response.status == 429:
                                    # Rate limited, wait longer
                                    await asyncio.sleep(5 * (retry + 1))
                                else:
                                    await asyncio.sleep(2 * (retry + 1))
                        except asyncio.TimeoutError:
                            if retry == 2:  # Last retry
                                raise
                            await asyncio.sleep(2 * (retry + 1))
                            continue

                    # Add delay between checks
                    await asyncio.sleep(30)  # Check every 30 seconds

                except Exception as e:
                    logging.error(f"Error during order tracking: {str(e)}")
                    await asyncio.sleep(5)  # Wait before retrying
                    continue

    except Exception as e:
        logger.error(f"❌ Error tracking order {order_id}: {str(e)}")
        logger.error(traceback.format_exc())
        try:
            await channel.send(f"❌ Error tracking order: {str(e)}")
        except discord.errors.Forbidden:
            logger.error(f"Cannot send messages in channel {channel.id}")

async def track(interaction: discord.Interaction, order_id: str):
    try:
        await interaction.response.defer(ephemeral=True)
        logger.info(f"📌 `/track` command triggered by {interaction.user} for order: {order_id}")

        # Send confirmation to user
        await interaction.followup.send("✅ Started tracking order!", ephemeral=True)

        # Get session
        session = await get_session()

        # Start tracking in background
        tracking_task = asyncio.create_task(track_order_status(order_id, interaction.channel, session))

        # Store the task for cleanup
        if not hasattr(bot, 'tracking_tasks'):
            bot.tracking_tasks = []
        bot.tracking_tasks.append(tracking_task)

        logger.info(f"Started tracking task for order {order_id}")

    except Exception as e:
        logger.error(f"❌ Error in track command: {str(e)}")
        logger.error(traceback.format_exc())
        await interaction.followup.send(
            "❌ An error occurred while processing the command.",
            ephemeral=True
        )

async def get_session() -> aiohttp.ClientSession:
    """Get or create the global HTTP session."""
    global _http_session
    if _http_session is None or _http_session.closed:
        _http_session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30),
            connector=aiohttp.TCPConnector(limit=20, ttl_dns_cache=300)
        )
    return _http_session

@bot.event
async def setup_hook():
    """Set up tasks before the bot starts."""
    # Initialize the HTTP session
    await get_session()

    # Record start time for uptime tracking
    bot.launch_time = time.time()

    # Initialize tracking tasks list
    bot.tracking_tasks = []

    logger.info("✅ Bot setup complete")

@bot.event
async def on_ready():
    """Called when the bot is ready."""
    logger.info(f"✅ Logged in as {bot.user}")
    logger.info(f"Command mode: {'Global' if COMMANDS_GLOBAL else 'Guild-only'}")

    # Memory usage info
    process = psutil.Process(os.getpid())
    memory_info = process.memory_info()
    logger.info(f"Memory usage: {memory_info.rss / 1024 / 1024:.2f} MB")

    try:
        if COMMANDS_GLOBAL:
            synced = await bot.tree.sync()
            logger.info(f"✅ Synced {len(synced)} command(s) globally")
        else:
            bot.tree.copy_global_to(guild=GUILD)
            synced = await bot.tree.sync(guild=GUILD)
            logger.info(f"✅ Synced {len(synced)} command(s) to guild")
    except Exception as e:
        logger.error(f"❌ Failed to sync commands: {e}")
        logger.error(traceback.format_exc())

@bot.event
async def on_message(message):
    if message.author == bot.user:
        return

    try:
        # Step 1: Extract group link
        group_link = await extract_group_link(message)
        if not group_link:
            return

        await message.channel.send("🔍 Group order link detected! Analyzing order...")

        # Step 2: Process group order
        result = await process_group_order(group_link)

        # Add the group link to the result dictionary
        if isinstance(result, dict):
            result['group_link'] = group_link

        # Step 3: Handle locked/error cases
        if isinstance(result, dict) and 'error' in result:
            if result['error'].get('type') == 'LOCKED_ORDER':
                await message.channel.send(embed=create_locked_order_embed())
                return

        if not result:
            await message.channel.send(embed=create_locked_order_embed())
            return

        # Step 4: Handle non-promo stores
        if not result.get('is_promo', False):
            await message.channel.send(embed=create_non_promo_embed())
            return

        # Step 5: Process cart items
        cart_items, _ = process_cart_items(result.get('cart_items', []))

        # Step 6: Use the fees directly from the API response
        fees_data = result.get('fees', {})
        if fees_data:
            subtotal = fees_data['subtotal']
            # Check if the subtotal contains 'CA' to determine if it's CAD
            is_cad = isinstance(subtotal, str) and 'CA' in subtotal
            # Clean up subtotal if it's a string
            if isinstance(subtotal, str):
                subtotal = float(subtotal.replace('CA', '').replace('$', '').strip())

            # Calculate overflow fee
            threshold = 30 if is_cad else 25
            overflow_fee = round(max(0, subtotal - threshold), 2)

            # Calculate discounted subtotal (30% of original)
            discounted_subtotal = round(subtotal * 0.3, 2)

            fee_calculations = {
                'subtotal': subtotal,
                'discounted_subtotal': discounted_subtotal,
                'savings': round(subtotal * 0.7, 2),
                'overflow_fee': overflow_fee,
                'service_fee': fees_data['service_fee'],
                'delivery_fee': fees_data['delivery_fee'],
                'ca_driver_benefit': fees_data['ca_driver_benefit'],
                'taxes': fees_data['taxes'],
                'total_fees': fees_data['total_fees_before_discount'],
                'uber_one_discount': fees_data['uber_one_discount'],
                'final_fees': fees_data['total_fees'],
                'final_total': round(discounted_subtotal + overflow_fee + fees_data['total_fees'], 2)
            }
        else:
            # Fallback if no fees data is available
            subtotal = 0
            is_cad = False
            fee_calculations = calculate_fees({}, 0, False)

        # Step 7: Create and send order summary
        summary_embed = create_order_summary_embed(result, cart_items, fee_calculations)
        await message.channel.send(embed=summary_embed)

        # Step 8: Check and send order limits warning if needed
        limit_warning = check_order_limits(subtotal, is_cad)
        if limit_warning:
            await message.channel.send(embed=limit_warning)
        else:
            await message.channel.send("# Hello! please wait patiently, the owner or the support team will assist you shortly, Thank you!")

    except Exception as e:
        logging.error(f"Error processing group order: {str(e)}")
        await message.channel.send(f"Error: {str(e)}")

async def latestsummary(
    interaction: discord.Interaction,
    subtotal: float,
    fees: float,
    grouporderlink: str = None
):
    try:
        logging.info(f"🚀 LATESTSUMMARY COMMAND START")
        logging.info(f"👤 Triggered by: {interaction.user} (ID: {interaction.user.id})")
        logging.info(f"💲 Input - Subtotal: ${subtotal:.2f}, Fees: ${fees:.2f}")
        logging.info(f"🔗 Group Order Link: {grouporderlink}")

        await interaction.response.defer()
        logging.info("⏳ Interaction deferred")

        # Initialize variables
        location = "Location not provided"
        cart_items = []

        # If no group order link provided, search in chat history
        if not grouporderlink:
            logging.info("🔍 No direct group order link - Searching channel history...")
            message_count = 0
            found_link = False

            async for message in interaction.channel.history(limit=50):
                message_count += 1
                logging.info(f"📝 Checking message {message_count}")

                pattern = r"https://(?:eats\.uber\.com/(?:[a-z]{2}(?:-[a-z]{2})?/)?group-orders/|www\.ubereats\.com/(?:[a-z]{2}(?:-[a-z]{2})?/)?group-orders/)[a-zA-Z0-9-]+(?:/join)?(?:\S*)"

                match = re.search(pattern, message.content)
                if match:
                    grouporderlink = match.group(0)
                    logging.info(f"✅ Found group order link: {grouporderlink}")
                    found_link = True
                    break

                for embed in message.embeds:
                    if embed.description:
                        match = re.search(pattern, embed.description)
                        if match:
                            grouporderlink = match.group(0)
                            logging.info(f"✅ Found group order link in embed: {grouporderlink}")
                            found_link = True
                            break

                if found_link:
                    break

        # Process group order if we have a link
        if grouporderlink:
            logging.info(f"🔄 Processing group order link...")
            result = await process_group_order(grouporderlink)

            if result:
                logging.info("✅ Group order processed successfully")

                loc = result.get('location', {})
                if loc:
                    location = f"{loc.get('address', 'Unknown')}, {loc.get('city', 'Unknown')}, {loc.get('state', 'XX').upper()}"
                    logging.info(f"📍 Location extracted: {location}")

                # Format cart items from dictionaries to strings
                raw_cart_items = result.get('cart_items', [])
                cart_items = []
                if raw_cart_items:
                    cart_items = format_cart_items(raw_cart_items)
                    logging.info(f"🛒 Retrieved {len(cart_items)} cart items")
                else:
                    logging.warning("⚠️ No cart items in result")

        # Calculate totals
        logging.info("💰 Calculating totals...")
        discounted_subtotal = round(subtotal * 0.30, 2)
        savings = round(subtotal - discounted_subtotal, 2)
        final_total = round(discounted_subtotal + fees, 2)

        logging.info(f"📊 Calculations complete:")
        logging.info(f"   Original: ${subtotal:.2f}")
        logging.info(f"   Discounted: ${discounted_subtotal:.2f}")
        logging.info(f"   Savings: ${savings:.2f}")
        logging.info(f"   Fees: ${fees:.2f}")
        logging.info(f"   Final: ${final_total:.2f}")

        # Create embed
        embed = discord.Embed(
            title="Group Order Summary",
            color=discord.Color.green()
        )

        embed.add_field(name="📍 Location", value=location, inline=False)
        if cart_items:
            # Use cart_items directly without creating a new variable
            embed.add_field(name="🛒 Cart Items", value="\n".join(cart_items), inline=False)
            embed.add_field(name="💸 Original Total", value=f"${subtotal:.2f}", inline=True)
            embed.add_field(name="💰 Discounted Total", value=f"${discounted_subtotal:.2f}", inline=True)
            embed.add_field(name="🤑 You Save", value=f"${savings:.2f}", inline=True)
            embed.add_field(name="📊 Fees & Tax", value=f"Fee & tax: ${fees:.2f}", inline=False)
            embed.add_field(name="🧾 Final Total (tip not included)", value=f"${final_total:.2f}", inline=False)
            embed.set_thumbnail(url="https://images-ext-1.discordapp.net/external/29FBokwLD5znXStCH2FvFcSsw-XO__J1aUZLmnpI5NI/https/i.gyazo.com/5dbb6a00ae9f91f522331b11d0053e86.gif?width=72&height=72")

        payment_view = PaymentMethodButtons()

        await interaction.followup.send(
            embed=embed,
            view=payment_view
        )
        logging.info("✅ LATESTSUMMARY COMMAND COMPLETE")

    except Exception as e:
        logging.error(f"❌ Error in latestsummary: {str(e)}")
        logging.error(f"📚 Full traceback:\n{traceback.format_exc()}")
        await interaction.followup.send(
            "❌ An error occurred while processing the summary.",
            ephemeral=True
        )

async def open_store(interaction: discord.Interaction):
    """Opens the store."""
    await interaction.response.defer(ephemeral=True)

    guild = interaction.guild
    status_channel = guild.get_channel(1348112700789620829)  # Status Channel

    if not status_channel:
        await interaction.followup.send("❌ Error: Status channel not found.", ephemeral=True)
        return

    # Create and send embed to STATUS CHANNEL
    embed = discord.Embed(
        title="🟢 Our Service is Now Open!",
        description="We are now open! Feel free to order.",
        color=discord.Color.green()
    )
    embed.set_image(url="https://media.discordapp.net/attachments/1349142062691520605/1356885369639014491/IMG_7286.png?ex=67ee3179&is=67ecdff9&hm=2c6c2fe28b8c8a7c5fe4d66a6b93f66f82df4052fd4c3044274a06c2a06b73b0&=&format=webp&quality=lossless&width=1366&height=777")
    embed.set_footer(text="Our Service is now open")

    # Send the embed to status channel
    await status_channel.send(embed=embed)

    # Send confirmation to user
    await interaction.followup.send("✅ Service has been opened!", ephemeral=True)

async def close_store(interaction: discord.Interaction):
    """Slash command to close the store."""
    guild = interaction.guild
    status_channel = guild.get_channel(1348112700789620829)  # Status Channel

    if not status_channel:
        await interaction.response.send_message("❌ Error: Status channel not found.", ephemeral=True)
        return

    # Create and send embed to the status channel
    embed = discord.Embed(
        title="🔴 Our Service is Now Closed!",
        description="We are now closed. Please come back later!",
        color=discord.Color.red()
    )
    embed.set_image(url="https://media.discordapp.net/attachments/1349142062691520605/1356885369639014491/IMG_7286.png?ex=67ee3179&is=67ecdff9&hm=2c6c2fe28b8c8a7c5fe4d66a6b93f66f82df4052fd4c3044274a06c2a06b73b0&=&format=webp&quality=lossless&width=1366&height=777")
    embed.set_footer(text="Our Service is currently closed. See you next time!")

    await interaction.response.send_message("✅ Service has been closed!", ephemeral=True)
    await status_channel.send(embed=embed)

@bot.tree.command(
    name="selectedstores",
    description="Get promo link for a specific address and save store list",
    guild=None if COMMANDS_GLOBAL else GUILD
)
@app_commands.default_permissions(send_messages=True, use_application_commands=True, read_message_history=True, read_messages=True, view_channel=True)
@app_commands.describe(
    address="The delivery address to check"
)
@app_commands.checks.cooldown(1, 30.0)  # 1 use per 30 seconds per user
async def selectedstores_command(interaction: discord.Interaction, address: str):
    """Gets a promo link for the specified address and saves store list."""
    await track_command_metrics("selectedstores")(selectedstores)(interaction, address)

@bot.tree.command(
    name="payments",
    description="Show payment details for Shams",
    guild=None if COMMANDS_GLOBAL else GUILD
)
@app_commands.default_permissions(send_messages=True, use_application_commands=True, read_message_history=True, read_messages=True, view_channel=True)
async def payments_command(interaction: discord.Interaction):
    """Sends Shams' payment details as an embed."""
    embed = discord.Embed(
        title="💰 Payment Details",
        color=discord.Color.blue()
    )
    embed.add_field(name="💵 CashApp/Applepay/Googlepay/Card", value="https://buy.stripe.com/14k02t418gEwbpS6oo", inline=False)
    embed.add_field(name="🪙 BTC", value="**********************************", inline=False)
    embed.add_field(name="🪙 LTC", value="MH1KG3LoJ35w4YQ1gHMTceNXg1Rdv1e6H1", inline=False)
    embed.add_field(name="🪙 Eth", value="******************************************", inline=False)
    embed.add_field(name="🪙 Sol", value="DsqasZShX5sg4kX18teS7S2ayrhDWhXUfa941v3agjMy", inline=False)
    embed.set_thumbnail(url="https://images-ext-1.discordapp.net/external/bRiKpP4Tw6Q1jqWW7nNDCU5y4zWcfyEeo0ooW5uFxz4/https/i.gyazo.com/e1abb32a09bb93d21e7e342cfdc44a1e.gif?width=72&height=72")

    await interaction.response.send_message(embed=embed)

@bot.tree.command(name="open",
    description="Opens the store."
)
@app_commands.checks.has_any_role(1348156363804774420, 1348117521688952954)
@app_commands.checks.cooldown(1, 60.0)  # 1 use per minute per user
async def open_store_slash(interaction: discord.Interaction):
    """Slash command to open the store."""
    await track_command_metrics("open_store")(open_store)(interaction)

@bot.tree.command(
    name="close",
    description="Closes the store."
)
@app_commands.guilds(GUILD)  # Fixed
@app_commands.checks.has_any_role(1348156363804774420, 1348117521688952954)  # Staff role check
@app_commands.checks.cooldown(1, 60.0)  # 1 use per minute per user
async def close_store_slash(interaction: discord.Interaction):
    """Slash command to close the store."""
    await track_command_metrics("close_store")(close_store)(interaction)


@bot.tree.command(
    name="track",
    description="Track an Uber order status",
    guild=None if COMMANDS_GLOBAL else GUILD
)
@app_commands.default_permissions(send_messages=True, use_application_commands=True)
@app_commands.describe(
    order_id="The Uber order ID to track"
)
@app_commands.checks.cooldown(1, 15.0)  # 1 use per 15 seconds per user
async def track_command(interaction: discord.Interaction, order_id: str):
    await track_command_metrics("track")(track)(interaction, order_id)

@bot.tree.command(
    name="latestsummary",
    description="Calculate order summary with manual subtotal and fees",
    guild=None if COMMANDS_GLOBAL else GUILD
)
@app_commands.describe(
    subtotal="Enter the original total amount (e.g., 21.28)",
    fees="Enter the fees and tax amount (e.g., 5.99)",
    grouporderlink="(Optional) Group order link to fetch location and items"
)
@app_commands.checks.cooldown(1, 5.0)  # 1 use per 5 seconds per user
async def latestsummary_command(
    interaction: discord.Interaction,
    subtotal: float,
    fees: float,
    grouporderlink: str = None
):
    """Wrapper for latestsummary function in bot.py"""
    await track_command_metrics("latestsummary")(latestsummary)(interaction, subtotal, fees, grouporderlink)


@bot.tree.command(
    name="storenotinpromo",
    description="Shows how to find eligible stores with promos",
    guild=None if COMMANDS_GLOBAL else GUILD
)
@app_commands.default_permissions(send_messages=True, use_application_commands=True, read_message_history=True, read_messages=True, view_channel=True)
@app_commands.checks.cooldown(1, 5.0)  # 1 use per 5 seconds per user
async def storenotinpromo(interaction: discord.Interaction):
    """Shows instructions for finding eligible stores with promos."""
    # Track command execution time
    start_time = time.time()

    try:
        # Check if we have this in cache
        cache_key = "storenotinpromo_embed"
        if cache_key in data_cache:
            logger.debug("Using cached storenotinpromo embed")
            embed = data_cache[cache_key]
        else:
            # Create the embed
            embed = discord.Embed(
                title="⚠️ Store Not in Promo",
                description="Follow these steps to find eligible stores:",
                color=discord.Color.yellow()
            )

            embed.set_image(url="https://media.discordapp.net/attachments/978162420423999498/1349998616110043237/image.png?ex=67d86f6f&is=67d71def&hm=43a18ef135008ca5afa451b11e762db1f2653cb163051d25e7194a16e87305a7&=&format=webp&quality=lossless")

            embed.add_field(
                name="Steps to Find Eligible Stores",
                value=(
                    "1. **Open this link in your Chrome/Safari Browser:**\n"
                    "[Eligible Restaurants](http://tiny.cc/52qc001)\n"
                    "\n"
                    "2. **Enter Your Address**\n"
                    "The link will take you to Uber Eats. Log in and Input your address and proceed.\n"
                    "\n"
                    "3. **Open the Link in a second tab in your Chrome/Safari Browser**\n"
                    "[Eligible Restaurants](http://tiny.cc/52qc001)\n"
                    "\n"
                    "4. **Done!**\n"
                    "You'll now see all eligible restaurants near you."
                ),
                inline=False
            )

            # Cache the embed for future use
            data_cache[cache_key] = embed
            logger.debug("Cached storenotinpromo embed")

        await interaction.response.send_message(embed=embed)

        # Track metrics
        execution_time = time.time() - start_time
        if "storenotinpromo" not in command_metrics:
            command_metrics["storenotinpromo"] = {'count': 0, 'total_time': 0, 'max_time': 0}

        command_metrics["storenotinpromo"]['count'] += 1
        command_metrics["storenotinpromo"]['total_time'] += execution_time
        command_metrics["storenotinpromo"]['max_time'] = max(
            command_metrics["storenotinpromo"]['max_time'],
            execution_time
        )

        logger.debug(f"Command storenotinpromo executed in {execution_time:.4f}s")
    except Exception as e:
        logger.error(f"Error in storenotinpromo command: {e}")
        logger.error(traceback.format_exc())
        if not interaction.response.is_done():
            await interaction.response.send_message(f"❌ An error occurred: {e}", ephemeral=True)

@bot.tree.command(
    name="ordersuccess",
    description="Track a successful order using order link",
    guild=None if COMMANDS_GLOBAL else GUILD
)
@app_commands.default_permissions(send_messages=True, use_application_commands=True, read_message_history=True, read_messages=True, view_channel=True)
@app_commands.describe(
    orderlink="The order link to track"
)
@app_commands.checks.cooldown(1, 15.0)  # 1 use per 15 seconds per user
async def ordersuccess_slash(
    interaction: discord.Interaction,
    orderlink: str
):
    """Slash command to track a successful order."""
    try:
        # Track command execution time for metrics
        command_start = time.time()

        logger.info(f"📌 `/ordersuccess` was triggered by {interaction.user} with order link: {orderlink}")
        await interaction.response.defer()

        # Handle channel renaming
        current_channel = interaction.channel
        if current_channel.name.startswith(("nelo4317-", "itscerv-", "_glitchyz-", "ticket-", "claimed-")):
            try:
                ticket_number = re.sub(r"^(nelo4317-|itscerv-|_glitchyz-|ticket-)", "", current_channel.name)
                new_name = f"{ticket_number}-delivering"
                await current_channel.edit(name=new_name)
                logger.info(f"✅ Successfully renamed channel to {new_name}")
            except Exception as e:
                logger.error(f"❌ Failed to rename channel: {str(e)}")
                logger.error(traceback.format_exc())

        # Extract order ID and fetch details
        order_id = re.search(r"orders/([a-f0-9-]+)", orderlink)
        if order_id:
            order_id = order_id.group(1)
            order_details = await fetch_order_details(order_id)

            if order_details:
                # Create the success embed
                order_embed = discord.Embed(
                    title="🎉 Order Successfully Placed!",
                    description=f"**Store:** {order_details['store']}\n**Estimated Arrival:** {order_details['eta']}",
                    color=discord.Color.green()
                )

                order_embed.add_field(name="🛒 Order Items", value=order_details['items'], inline=False)
                order_embed.add_field(name="📍 Delivery Address", value=order_details['address'], inline=False)
                order_embed.add_field(name="👤 Customer", value=order_details['customer'], inline=True)
                order_embed.add_field(name="🔗 Order Link", value=orderlink, inline=False)

                # Create view with tracking button
                view = OrderTrackButton(orderlink)

                # Send the embed with the view
                await interaction.followup.send(embed=order_embed, view=view)

                # Get session
                session = await get_session()

                # Start tracking immediately and ensure it's running in the background
                tracking_task = asyncio.create_task(track_order_status(orderlink, interaction.channel, session))
                logger.info(f"✅ Tracking task created for order {order_id}")

                # Store the task for cleanup
                if not hasattr(bot, 'tracking_tasks'):
                    bot.tracking_tasks = []
                bot.tracking_tasks.append(tracking_task)
            else:
                await interaction.followup.send("❌ Failed to fetch order details.", ephemeral=True)
        else:
            await interaction.followup.send("❌ Invalid order link format.", ephemeral=True)
            return

        # Track metrics at the end of successful execution
        execution_time = time.time() - command_start
        if "ordersuccess" not in command_metrics:
            command_metrics["ordersuccess"] = {'count': 0, 'total_time': 0, 'max_time': 0}

        command_metrics["ordersuccess"]['count'] += 1
        command_metrics["ordersuccess"]['total_time'] += execution_time
        command_metrics["ordersuccess"]['max_time'] = max(
            command_metrics["ordersuccess"]['max_time'],
            execution_time
        )

        logger.info("✅ Order tracking initiated.")
        logger.debug(f"Command ordersuccess executed in {execution_time:.4f}s")

    except Exception as e:
        logger.error(f"Error in ordersuccess command: {e}")
        logger.error(traceback.format_exc())
        try:
            if interaction.response.is_done():
                await interaction.followup.send(f"❌ An error occurred: {e}", ephemeral=True)
            else:
                await interaction.response.send_message(f"❌ An error occurred: {e}", ephemeral=True)
        except Exception:
            pass

# Command metrics decorator
def track_command_metrics(command_name):
    """Decorator to track command execution metrics."""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                return await func(*args, **kwargs)
            finally:
                execution_time = time.time() - start_time
                if command_name not in command_metrics:
                    command_metrics[command_name] = {
                        'count': 0,
                        'total_time': 0,
                        'max_time': 0
                    }

                command_metrics[command_name]['count'] += 1
                command_metrics[command_name]['total_time'] += execution_time
                command_metrics[command_name]['max_time'] = max(
                    command_metrics[command_name]['max_time'],
                    execution_time
                )

                logger.debug(
                    f"Command {command_name} executed in {execution_time:.4f}s "
                    f"(avg: {command_metrics[command_name]['total_time'] / command_metrics[command_name]['count']:.4f}s)"
                )
        return wrapper
    return decorator

# Add a metrics command to show performance statistics
@bot.tree.command(
    name="metrics",
    description="Show bot performance metrics",
    guild=None if COMMANDS_GLOBAL else GUILD
)
@app_commands.checks.has_any_role(1348156363804774420, 1348117521688952954)  # Staff role check
async def metrics_command(interaction: discord.Interaction):
    """Show bot performance metrics."""
    try:
        # Get memory usage
        process = psutil.Process(os.getpid())
        memory_info = process.memory_info()
        memory_usage = memory_info.rss / 1024 / 1024  # MB

        # Get CPU usage
        cpu_percent = process.cpu_percent(interval=0.5)

        # Create embed
        embed = discord.Embed(
            title="📊 Bot Performance Metrics",
            description=f"Performance statistics for {bot.user.name}",
            color=discord.Color.blue()
        )

        # Add system metrics
        embed.add_field(
            name="💽 System Metrics",
            value=f"**Memory Usage:** {memory_usage:.2f} MB\n"
                  f"**CPU Usage:** {cpu_percent:.1f}%\n"
                  f"**Uptime:** {time.time() - bot.launch_time:.1f} seconds",
            inline=False
        )

        # Add command metrics
        if command_metrics:
            # Sort by count
            sorted_commands = sorted(
                command_metrics.items(),
                key=lambda x: x[1]['count'],
                reverse=True
            )

            commands_value = ""
            for cmd_name, metrics in sorted_commands[:10]:  # Show top 10
                avg_time = metrics['total_time'] / metrics['count']
                commands_value += f"**{cmd_name}:** {metrics['count']} calls, "
                commands_value += f"avg: {avg_time*1000:.1f}ms, "
                commands_value += f"max: {metrics['max_time']*1000:.1f}ms\n"

            embed.add_field(
                name="📋 Command Metrics (Top 10)",
                value=commands_value or "No commands executed yet.",
                inline=False
            )
        else:
            embed.add_field(
                name="📋 Command Metrics",
                value="No commands executed yet.",
                inline=False
            )

        # Add background tasks info
        tasks_info = ""
        if hasattr(bot, 'tracking_tasks'):
            active_tracking = sum(1 for t in bot.tracking_tasks if not t.done())
            tasks_info += f"**Order Tracking Tasks:** {active_tracking}/{len(bot.tracking_tasks)}\n"

        embed.add_field(
            name="⏳ Background Tasks",
            value=tasks_info or "No background tasks.",
            inline=False
        )

        await interaction.response.send_message(embed=embed, ephemeral=True)
    except Exception as e:
        logger.error(f"Error in metrics command: {e}")
        logger.error(traceback.format_exc())
        await interaction.response.send_message(f"❌ Error: {e}", ephemeral=True)

# Command error handler
@bot.event
async def on_command_error(ctx, error):
    """Handle command errors."""
    if isinstance(error, commands.CommandOnCooldown):
        await ctx.send(f"⏳ Command on cooldown. Try again in {error.retry_after:.1f} seconds.")
    elif isinstance(error, commands.MissingPermissions):
        await ctx.send("⛔ You don't have permission to use this command.")
    else:
        logger.error(f"Command error: {error}")
        logger.error(traceback.format_exc())
        await ctx.send(f"❌ An error occurred: {error}")

# Process the command queue
async def process_command_queue():
    """Process queued commands when they're off cooldown."""
    global queue_processor_running
    queue_processor_running = True

    try:
        while True:
            # Check if there are any commands in the queue
            commands_to_process = []
            current_time = time.time()

            for command_name, queue in command_queue.items():
                # Filter out commands that are ready to be processed
                ready_commands = [cmd for cmd in queue if current_time >= cmd['ready_time']]

                for cmd in ready_commands:
                    commands_to_process.append((command_name, cmd))
                    queue.remove(cmd)

            # Process commands that are ready
            for command_name, cmd in commands_to_process:
                try:
                    logger.info(f"Processing queued command: {command_name}")

                    # Get the original command function and arguments
                    func = cmd['func']
                    args = cmd['args']
                    kwargs = cmd['kwargs']

                    # Execute the command with a wrapper to handle already responded interactions
                    interaction = args[0] if args else None

                    # Create a special wrapper for the interaction to prevent defer errors
                    if interaction and hasattr(interaction, 'response'):
                        # Create a backup of the original defer method
                        original_defer = interaction.response.defer

                        # Replace with a version that doesn't error if already responded
                        async def safe_defer(*args, **kwargs):
                            try:
                                return await original_defer(*args, **kwargs)
                            except discord.errors.InteractionResponded:
                                logger.debug(f"Interaction already responded to, ignoring defer")
                                return None

                        # Swap the method
                        interaction.response.defer = safe_defer

                    # Now execute the command
                    await func(*args, **kwargs)

                    # Notify the user that their command has been processed
                    interaction = args[0] if args else None
                    if interaction and hasattr(interaction, 'followup'):
                        try:
                            await interaction.followup.send(
                                f"✅ Your queued command `/{command_name}` has been processed!",
                                ephemeral=True
                            )
                        except Exception as e:
                            logger.error(f"Failed to notify user about queued command: {e}")
                except Exception as e:
                    logger.error(f"Error processing queued command {command_name}: {e}")
                    logger.error(traceback.format_exc())

            # If all queues are empty, check if we should exit
            if all(len(queue) == 0 for queue in command_queue.values()):
                # Wait a bit to see if new commands come in
                await asyncio.sleep(1)
                if all(len(queue) == 0 for queue in command_queue.values()):
                    break

            # Wait before checking again
            await asyncio.sleep(0.5)
    except Exception as e:
        logger.error(f"Error in command queue processor: {e}")
        logger.error(traceback.format_exc())
    finally:
        queue_processor_running = False

# Application command error handler
@bot.tree.error
async def on_app_command_error(interaction: discord.Interaction, error: app_commands.AppCommandError):
    """Handle application command errors."""
    if isinstance(error, app_commands.CommandOnCooldown):
        # Get the command that was called
        command_name = interaction.command.name if interaction.command else "unknown"

        # Get the original command function
        command_func = interaction.command._callback if interaction.command else None

        if command_func:
            # Calculate when the command will be ready
            ready_time = time.time() + error.retry_after

            # Initialize the queue for this command if it doesn't exist
            if command_name not in command_queue:
                command_queue[command_name] = []

            # Capture all the parameters from the interaction
            kwargs = {}

            # Get the command parameters from the namespace
            if hasattr(interaction, 'namespace') and interaction.namespace:
                for param_name in interaction.namespace.__dict__:
                    if param_name.startswith('_'):
                        continue  # Skip private attributes
                    param_value = getattr(interaction.namespace, param_name)
                    kwargs[param_name] = param_value

            logger.debug(f"Queueing command {command_name} with kwargs: {kwargs}")

            # Add the command to the queue
            command_queue[command_name].append({
                'func': command_func,
                'args': [interaction],  # Most commands take interaction as first arg
                'kwargs': kwargs,  # Add captured kwargs
                'ready_time': ready_time,
                'user_id': interaction.user.id
            })

            # Start the queue processor if it's not already running
            global queue_processor_running
            if not queue_processor_running:
                asyncio.create_task(process_command_queue())

            # Inform the user that their command has been queued
            await interaction.response.send_message(
                f"⏳ Command `/{command_name}` is on cooldown. It has been queued and will be processed in {error.retry_after:.1f} seconds.",
                ephemeral=True
            )
        else:
            # Fallback to the original behavior if we can't get the command function
            await interaction.response.send_message(
                f"⏳ Command on cooldown. Try again in {error.retry_after:.1f} seconds.",
                ephemeral=True
            )
    elif isinstance(error, app_commands.MissingPermissions):
        await interaction.response.send_message(
            "⛔ You don't have permission to use this command.",
            ephemeral=True
        )
    else:
        logger.error(f"App command error: {error}")
        logger.error(traceback.format_exc())

        # Try to respond if not already responded
        try:
            if interaction.response.is_done():
                await interaction.followup.send(
                    f"❌ An error occurred: {error}",
                    ephemeral=True
                )
            else:
                await interaction.response.send_message(
                    f"❌ An error occurred: {error}",
                    ephemeral=True
                )
        except Exception as e:
            logger.error(f"Failed to send error message: {e}")

# Graceful shutdown
async def cleanup():
    """Clean up resources before shutdown."""
    logger.info("Cleaning up resources...")

    # Cancel tracking tasks
    if hasattr(bot, 'tracking_tasks'):
        for task in bot.tracking_tasks:
            if not task.done():
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass

    # Close HTTP session
    global _http_session
    if _http_session and not _http_session.closed:
        await _http_session.close()
        logger.info("HTTP session closed")

if __name__ == "__main__":
    try:
        # Run the bot with proper signal handling
        bot.run(DISCORD_BOT_TOKEN, log_handler=None)
    except KeyboardInterrupt:
        logger.info("Bot stopped by keyboard interrupt")
    except Exception as e:
        logger.error(f"Failed to start bot: {e}")
        logger.error(traceback.format_exc())
    finally:
        # Run cleanup in a new event loop
        if not asyncio.get_event_loop().is_closed():
            asyncio.run(cleanup())