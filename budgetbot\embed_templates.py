import discord
import logging
from typing import List, Dict, Any, Optional

logger = logging.getLogger('budgetbot.embeds')

def create_locked_order_embed():
    """Create a modern embed for locked/canceled orders."""
    embed = discord.Embed(
        title="🔒 Group Order is Locked",
        description="This group order appears to be locked or canceled.",
        color=discord.Color.from_rgb(237, 66, 69)  # Discord Red
    )

    # Add a clean divider
    embed.add_field(
        name="",
        value="━━━━━━━━━━━━━━━━━━━━━━━━━",
        inline=False
    )

    # Add steps with better formatting
    embed.add_field(
        name="🔸 Step 1",
        value="Go back to your Uber Eats Cart",
        inline=False
    )

    embed.add_field(
        name="🔹 Step 2",
        value="Press **Unlock Group Order**",
        inline=False
    )

    embed.add_field(
        name="🔸 Step 3",
        value="Resend your cart link to this channel",
        inline=False
    )

    # Set a nice thumbnail
    embed.set_thumbnail(url="https://i.imgur.com/JqA9FeL.png")  # Lock icon

    # Add footer with helpful tip
    embed.set_footer(text="If you continue to have issues, please contact support")

    return embed

def create_non_promo_embed():
    """Create a modern embed for stores not in promo."""
    embed = discord.Embed(
        title="🔎 Find Eligible Stores",
        description="**The store you're looking for isn't in our promo. Follow these steps to find eligible restaurants:**",
        color=discord.Color.from_rgb(254, 231, 92)  # Bright Yellow
    )

    # Add a clean divider
    embed.add_field(
        name="",
        value="━━━━━━━━━━━━━━━━━━━━━━━━━",
        inline=False
    )

    # Step 1
    embed.add_field(
        name="🔸 Step 1: Open Our Special Link",
        value="Open [**this link**](http://tiny.cc/52qc001) in Chrome or Safari",
        inline=False
    )

    # Step 2
    embed.add_field(
        name="🔹 Step 2: Enter Your Address",
        value="Log in to Uber Eats and enter your delivery address",
        inline=False
    )

    # Step 3
    embed.add_field(
        name="🔸 Step 3: Open Link Again",
        value="Open [**the same link**](http://tiny.cc/52qc001) in a second tab",
        inline=False
    )

    # Step 4
    embed.add_field(
        name="🔹 Step 4: Browse Eligible Restaurants",
        value="You'll now see all restaurants eligible for our promo!",
        inline=False
    )

    # Set a thumbnail instead of full image for cleaner look
    embed.set_thumbnail(url="https://i.imgur.com/JNRmHjM.png")  # Search/store icon

    # Add a footer with a tip
    embed.set_footer(text="Tip: If you still don't see eligible stores, try clearing your browser cookies")

    # Add the screenshot as a smaller image at the bottom
    embed.set_image(url="https://media.discordapp.net/attachments/978162420423999498/1349998616110043237/image.png?width=600&height=338")

    return embed

def create_beta_group_order_summary_embed(result: Dict[str, Any], cart_items: List[str] = None, fee_calculations: Dict[str, Any] = None) -> discord.Embed:
    """Create a modern beta group order summary embed with enhanced visuals."""
    embed = discord.Embed(
        title="🍽️ Budget Order Summary",
        color=discord.Color.from_rgb(87, 242, 135)  # Vibrant Green
    )

    # Add group order link if available with better formatting
    group_link = result.get('group_link', 'Not available')

    # Check if the store is in promo
    is_promo = result.get('is_promo', False)
    promo_status = "✅ Store is in promo!" if is_promo else "❌ Store is not in promo"

    embed.description = f"**🎟️ Promo Status:** {promo_status}\n**🔗 [Group Order Link]({group_link})**\n\n"

    # Add a clean divider
    embed.add_field(
        name="",
        value="━━━━━━━━━━━━━━━━━━━━━━━━━",
        inline=False
    )

    # Location with better formatting
    location = result.get('location', {})
    location_str = f"{location.get('address', 'Unknown Address')}"
    if location.get('city'):
        location_str += f", {location.get('city')}"
    if location.get('state'):
        location_str += f", {location.get('state').upper()}"
    if location.get('zipcode'):
        location_str += f" {location.get('zipcode')}"

    embed.add_field(
        name="📍 Delivery Location",
        value=location_str,
        inline=False
    )

    # Add restaurant info
    restaurant_name = result.get('restaurant_name', 'Unknown Restaurant')
    if not restaurant_name or restaurant_name == 'Unknown Restaurant':
        # Try to get it from store_name if available
        restaurant_name = result.get('store_name', 'Unknown Restaurant')
    
    # Add store URL if available
    store_url = result.get('store_url', '')
    if store_url:
        embed.add_field(
            name="🍴 Restaurant",
            value=f"[{restaurant_name}]({store_url})",
            inline=False
        )
    else:
        embed.add_field(
            name="🍴 Restaurant",
            value=restaurant_name,
            inline=False
        )

    # Process cart items if provided
    if not cart_items and 'cart_items' in result:
        raw_cart_items = result.get('cart_items', [])
        if raw_cart_items:
            # Simple formatting for preview
            cart_items = []
            for item in raw_cart_items:
                title = item.get('title', 'Unknown Item')
                quantity = item.get('quantity', 1)
                cart_items.append(f"x{quantity} {title}")

    # Add cart items with better formatting
    if cart_items and len(cart_items) > 0:
        # Limit to 10 items to avoid embed being too large
        items_to_show = cart_items[:10]
        remaining_items = len(cart_items) - 10 if len(cart_items) > 10 else 0

        items_str = "\n".join([f"• {item}" for item in items_to_show])
        if remaining_items > 0:
            items_str += f"\n• ... and {remaining_items} more items"

        embed.add_field(
            name=f"🛒 Order Items ({len(cart_items)})",
            value=items_str,
            inline=False
        )

    # Set a nice thumbnail
    embed.set_thumbnail(url="https://i.imgur.com/GwSvqWD.png")  # Food/order icon

    # Add footer with helpful information
    if is_promo:
        embed.set_footer(text="A Chef or Waiter will assist you shortly | Budget Service")
    else:
        embed.set_footer(text="Please see our recommendations for eligible restaurants below")

    return embed

def create_order_summary_embed(result: Dict[str, Any], cart_items: List[str], fee_calculations: Dict[str, Any]) -> discord.Embed:
    """Create a modern order summary embed with enhanced visuals."""
    embed = discord.Embed(
        title="🍽️ Budget Order Summary",
        color=discord.Color.from_rgb(87, 242, 135)  # Vibrant Green
    )

    # Add group order link if available with better formatting
    group_link = result.get('group_link', 'Not available')

    # Check if the store is in promo
    is_promo = result.get('is_promo', True)
    promo_status = "✅ Store is in promo!" if is_promo else "❌ Store is not in promo"

    embed.description = f"**🎟️ Promo Status:** {promo_status}\n**🔗 [Group Order Link]({group_link})**\n\n"

    # Add a clean divider
    embed.add_field(
        name="",
        value="━━━━━━━━━━━━━━━━━━━━━━━━━",
        inline=False
    )

    # Location with better formatting
    location = result.get('location', {})
    location_str = f"{location.get('address', 'Unknown Address')}"
    if location.get('city'):
        location_str += f", {location.get('city')}"
    if location.get('state'):
        location_str += f", {location.get('state').upper()}"
    if location.get('zipcode'):
        location_str += f" {location.get('zipcode')}"

    embed.add_field(
        name="📍 Delivery Location",
        value=location_str,
        inline=False
    )

    # Add store URL if available
    store_url = result.get('store_url', '')
    if store_url:
        embed.add_field(
            name="🏪 Restaurant",
            value=f"[View on Uber Eats]({store_url})",
            inline=False
        )

    # Add cart items with better formatting
    if cart_items:
        # Limit to 10 items to avoid embed being too large
        items_to_show = cart_items[:10]
        remaining_items = len(cart_items) - 10 if len(cart_items) > 10 else 0

        items_str = "\n".join([f"• {item}" for item in items_to_show])
        if remaining_items > 0:
            items_str += f"\n• ... and {remaining_items} more items"

        embed.add_field(
            name=f"🛒 Order Items ({len(cart_items)})",
            value=items_str,
            inline=False
        )

    # Price breakdown with better formatting
    currency = "CA$" if fee_calculations.get('is_cad', False) else "$"

    # Calculate new pricing structure
    original_subtotal = fee_calculations.get('subtotal', 0)

    # Special case for $24 subtotal
    if original_subtotal == 24.00:
        discounted_subtotal = 0.00
        discount_amount = original_subtotal
    else:
        # Fixed $25 discount
        discount_amount = 25.00
        discounted_subtotal = max(0, original_subtotal - discount_amount)

    savings = original_subtotal - discounted_subtotal

    # Original vs. discounted price
    embed.add_field(
        name="💰 Price Breakdown",
        value=f"**Original Subtotal:** `{currency}{original_subtotal:.2f}`\n" +
              f"**Discount:** `-{currency}{discount_amount:.2f}`\n" +
              f"**Discounted Subtotal:** `{currency}{discounted_subtotal:.2f}`\n" +
              f"**You Save:** `{currency}{savings:.2f}`",
        inline=False
    )

    # Fees breakdown with fixed $10 service fee
    fees_str = ""

    if fee_calculations.get('overflow_fee', 0) > 0:
        fees_str += f"**Overflow Fee:** `{currency}{fee_calculations.get('overflow_fee', 0):.2f}`\n"

    # Fixed $10 service fee
    fees_str += f"**Service Fee:** `{currency}10.00`\n"

    if fee_calculations.get('ca_driver_benefit', 0) > 0:
        fees_str += f"**Driver Benefit:** `{currency}{fee_calculations.get('ca_driver_benefit', 0):.2f}`\n"

    fees_str += f"**Taxes:** `{currency}{fee_calculations.get('taxes', 0):.2f}`"

    embed.add_field(
        name="💸 Fees & Taxes",
        value=fees_str,
        inline=False
    )

    # Calculate new final total
    total_fees = 10.00  # Fixed service fee
    if fee_calculations.get('overflow_fee', 0) > 0:
        total_fees += fee_calculations.get('overflow_fee', 0)
    if fee_calculations.get('ca_driver_benefit', 0) > 0:
        total_fees += fee_calculations.get('ca_driver_benefit', 0)
    total_fees += fee_calculations.get('taxes', 0)

    final_total = discounted_subtotal + total_fees

    # Final total with emphasis
    embed.add_field(
        name="💲 Estimated Final Total",
        value=f"**`{currency}{final_total:.2f}`**",
        inline=False
    )

    # Set a nice thumbnail
    embed.set_thumbnail(url="https://i.imgur.com/GwSvqWD.png")  # Food/order icon

    # Add footer with helpful information
    embed.set_footer(text="A Chef or Waiter will assist you shortly | Budget Service")

    return embed

def check_order_limits(subtotal: float, is_cad: bool) -> Optional[discord.Embed]:
    """Check if order meets minimum/maximum requirements with modern embed."""
    currency = "CA$" if is_cad else "$"
    min_order = 30.0 if is_cad else 24.0
    max_order = 35.0 if is_cad else 30.0

    if subtotal < min_order:
        embed = discord.Embed(
            title="⚠️ Minimum Order Requirement",
            description=f"Your subtotal must be at least `{currency}{min_order:.2f}` before fees and taxes.",
            color=discord.Color.from_rgb(254, 231, 92)  # Bright Yellow
        )

        # Add a clean divider
        embed.add_field(
            name="",
            value="━━━━━━━━━━━━━━━━━━━━━━━━━",
            inline=False
        )

        # Add action required
        embed.add_field(
            name="👉 Action Required",
            value="Please add more items to your cart to meet the minimum requirement.",
            inline=False
        )

        # Add current vs required
        embed.add_field(
            name="📊 Current vs Required",
            value=f"**Current Subtotal:** `{currency}{subtotal:.2f}`\n" +
                  f"**Minimum Required:** `{currency}{min_order:.2f}`\n" +
                  f"**Amount to Add:** `{currency}{(min_order - subtotal):.2f}`",
            inline=False
        )

        # Set a nice thumbnail
        embed.set_thumbnail(url="https://i.imgur.com/YKI4LbC.png")  # Warning icon

        return embed

    elif subtotal > max_order:
        embed = discord.Embed(
            title="⚠️ Maximum Order Limit",
            description=f"Your subtotal must be at most `{currency}{max_order:.2f}` before fees and taxes.",
            color=discord.Color.from_rgb(254, 231, 92)  # Bright Yellow
        )

        # Add a clean divider
        embed.add_field(
            name="",
            value="━━━━━━━━━━━━━━━━━━━━━━━━━",
            inline=False
        )

        # Add action required
        embed.add_field(
            name="👉 Action Required",
            value="Please remove some items from your cart to meet the maximum limit.",
            inline=False
        )

        # Add current vs required
        embed.add_field(
            name="📊 Current vs Limit",
            value=f"**Current Subtotal:** `{currency}{subtotal:.2f}`\n" +
                  f"**Maximum Allowed:** `{currency}{max_order:.2f}`\n" +
                  f"**Amount to Remove:** `{currency}{(subtotal - max_order):.2f}`",
            inline=False
        )

        # Set a nice thumbnail
        embed.set_thumbnail(url="https://i.imgur.com/YKI4LbC.png")  # Warning icon

        return embed

    return None

def create_error_embed(error_message: str) -> discord.Embed:
    """Create a modern error embed."""
    embed = discord.Embed(
        title="❌ Error Processing Order",
        description=f"We encountered an error while processing your order:\n\n```{error_message}```",
        color=discord.Color.from_rgb(237, 66, 69)  # Discord Red
    )

    # Add a clean divider
    embed.add_field(
        name="",
        value="━━━━━━━━━━━━━━━━━━━━━━━━━",
        inline=False
    )

    # Add troubleshooting steps
    embed.add_field(
        name="🔍 Troubleshooting Steps",
        value="1. Make sure your group order is unlocked\n" +
              "2. Try creating a new group order\n" +
              "3. Check that you're using a supported restaurant",
        inline=False
    )

    # Set a nice thumbnail
    embed.set_thumbnail(url="https://i.imgur.com/YKI4LbC.png")  # Warning icon

    # Add footer with support info
    embed.set_footer(text="If the issue persists, please contact support")

    return embed

def create_processing_embed() -> discord.Embed:
    """Create a modern processing embed."""
    embed = discord.Embed(
        title="🔄 Processing Your Order",
        description="We're analyzing your Uber Eats group order. This may take a few seconds...",
        color=discord.Color.from_rgb(88, 101, 242)  # Discord Blurple
    )

    # Set a nice thumbnail
    embed.set_thumbnail(url="https://i.imgur.com/3gL0FzN.gif")  # Loading animation

    return embed

def create_store_status_embed(is_open: bool) -> discord.Embed:
    """Create a modern store status embed."""
    if is_open:
        title = "🟢 Our Service is Now Open!"
        description = "We are now open! Feel free to order."
        color = discord.Color.from_rgb(87, 242, 135)  # Vibrant Green
        footer_text = "Our Service is now open"
    else:
        title = "🔴 Our Service is Now Closed!"
        description = "We are now closed. Please come back later!"
        color = discord.Color.from_rgb(237, 66, 69)  # Discord Red
        footer_text = "Our Service is currently closed. See you next time!"

    embed = discord.Embed(
        title=title,
        description=description,
        color=color
    )

    # Add a clean divider
    embed.add_field(
        name="",
        value="━━━━━━━━━━━━━━━━━━━━━━━━━",
        inline=False
    )

    if is_open:
        embed.add_field(
            name="📋 Service Information",
            value="• Fixed $25 discount on all orders\n• $24-$30 subtotal range\n• Fast processing times",
            inline=False
        )

    # Set image
    embed.set_image(url="https://media.discordapp.net/attachments/1349142062691520605/1356885369639014491/IMG_7286.png?ex=67ee3179&is=67ecdff9&hm=2c6c2fe28b8c8a7c5fe4d66a6b93f66f82df4052fd4c3044274a06c2a06b73b0&=&format=webp&quality=lossless&width=1366&height=777")

    # Set footer
    embed.set_footer(text=footer_text)

    return embed

def create_payment_embed() -> discord.Embed:
    """Create a modern payment details embed."""
    embed = discord.Embed(
        title="💳 Payment Details",
        description="Choose your preferred payment method below:",
        color=discord.Color.from_rgb(88, 101, 242)  # Discord Blurple
    )

    # Add a clean divider
    embed.add_field(
        name="",
        value="━━━━━━━━━━━━━━━━━━━━━━━━━",
        inline=False
    )

    embed.add_field(
        name="💵 Payment Options",
        value="**CashApp / Apple Pay / Google Pay / Card**\n[Click here to pay](https://buy.stripe.com/14k02t418gEwbpS6oo)",
        inline=False
    )

    # Add a clean divider
    embed.add_field(
        name="",
        value="━━━━━━━━━━━━━━━━━━━━━━━━━",
        inline=False
    )

    # Add instructions
    embed.add_field(
        name="📝 Instructions",
        value="1. Click the payment link above\n2. Complete the payment process\n3. Return to Discord and let us know when payment is complete",
        inline=False
    )

    # Set a nice thumbnail
    embed.set_thumbnail(url="https://images-ext-1.discordapp.net/external/bRiKpP4Tw6Q1jqWW7nNDCU5y4zWcfyEeo0ooW5uFxz4/https/i.gyazo.com/e1abb32a09bb93d21e7e342cfdc44a1e.gif?width=72&height=72")

    # Add footer
    embed.set_footer(text="Secure payments processed by Stripe")

    return embed

def create_metrics_embed(bot_name: str, memory_usage: float, cpu_percent: float, uptime: float,
                        command_metrics: Dict[str, Dict[str, float]], active_tracking: int,
                        total_tracking: int) -> discord.Embed:
    """Create a modern metrics embed."""
    embed = discord.Embed(
        title="📊 Bot Performance Metrics",
        description=f"Performance statistics for {bot_name}",
        color=discord.Color.from_rgb(88, 101, 242)  # Discord Blurple
    )

    # Add a clean divider
    embed.add_field(
        name="",
        value="━━━━━━━━━━━━━━━━━━━━━━━━━",
        inline=False
    )

    # Add system metrics
    embed.add_field(
        name="💽 System Metrics",
        value=f"**Memory Usage:** `{memory_usage:.2f} MB`\n" +
              f"**CPU Usage:** `{cpu_percent:.1f}%`\n" +
              f"**Uptime:** `{uptime:.1f} seconds`",
        inline=False
    )

    # Add command metrics
    if command_metrics:
        # Sort by count
        sorted_commands = sorted(
            command_metrics.items(),
            key=lambda x: x[1]['count'],
            reverse=True
        )

        commands_value = ""
        for cmd_name, metrics in sorted_commands[:10]:  # Show top 10
            avg_time = metrics['total_time'] / metrics['count']
            commands_value += f"**{cmd_name}:** `{metrics['count']}` calls, "
            commands_value += f"avg: `{avg_time*1000:.1f}ms`, "
            commands_value += f"max: `{metrics['max_time']*1000:.1f}ms`\n"

        embed.add_field(
            name="📋 Command Metrics (Top 10)",
            value=commands_value or "No commands executed yet.",
            inline=False
        )
    else:
        embed.add_field(
            name="📋 Command Metrics",
            value="No commands executed yet.",
            inline=False
        )

    # Add background tasks info
    tasks_info = f"**Order Tracking Tasks:** `{active_tracking}/{total_tracking}`\n"

    embed.add_field(
        name="⏳ Background Tasks",
        value=tasks_info or "No background tasks.",
        inline=False
    )

    # Set a nice thumbnail
    embed.set_thumbnail(url="https://i.imgur.com/3gL0FzN.gif")

    return embed

def create_latestsummary_embed(location: str, cart_items: List[str], subtotal: float,
                              discounted_subtotal: float, savings: float, fees: float,
                              final_total: float) -> discord.Embed:
    """Create a modern latestsummary embed with updated pricing structure."""
    embed = discord.Embed(
        title="🍽️ Budget Order Summary",
        color=discord.Color.from_rgb(87, 242, 135)  # Vibrant Green
    )

    # Add a clean divider
    embed.add_field(
        name="",
        value="━━━━━━━━━━━━━━━━━━━━━━━━━",
        inline=False
    )

    embed.add_field(name="📍 Location", value=location, inline=False)

    if cart_items:
        # Limit to 10 items to avoid embed being too large
        items_to_show = cart_items[:10]
        remaining_items = len(cart_items) - 10 if len(cart_items) > 10 else 0

        items_str = "\n".join([f"• {item}" for item in items_to_show])
        if remaining_items > 0:
            items_str += f"\n• ... and {remaining_items} more items"

        embed.add_field(name="🛒 Cart Items", value=items_str, inline=False)

    # Add a clean divider
    embed.add_field(
        name="",
        value="━━━━━━━━━━━━━━━━━━━━━━━━━",
        inline=False
    )

    # Calculate pricing with new structure
    # Special case for $24 subtotal
    if subtotal == 24.00:
        actual_discounted_subtotal = 0.00
        discount_amount = subtotal
    else:
        # Fixed $25 discount
        discount_amount = 25.00
        actual_discounted_subtotal = max(0, subtotal - discount_amount)

    actual_savings = subtotal - actual_discounted_subtotal

    # Calculate final total with fixed $10 service fee
    actual_final_total = actual_discounted_subtotal + 10.00 + (fees - 10.00 if fees > 10.00 else 0)

    # Price breakdown
    embed.add_field(
        name="💰 Price Breakdown",
        value=f"**Original Total:** `${subtotal:.2f}`\n" +
              f"**Discount:** `-${discount_amount:.2f}`\n" +
              f"**Discounted Subtotal:** `${actual_discounted_subtotal:.2f}`\n" +
              f"**You Save:** `${actual_savings:.2f}`",
        inline=False
    )

    # Fees and final total
    embed.add_field(name="📊 Fees & Tax", value=f"**Service Fee & Tax:** `${10.00 + (fees - 10.00 if fees > 10.00 else 0):.2f}`", inline=False)
    embed.add_field(name="🧾 Estimated Final Total (tip not included)", value=f"**`${actual_final_total:.2f}`**", inline=False)

    # Set a nice thumbnail
    embed.set_thumbnail(url="https://i.imgur.com/GwSvqWD.png")

    # Add footer with helpful information
    embed.set_footer(text="Note: Final price may vary slightly. Please wait for confirmation.")

    return embed
