import logging

# Global logging configuration
DEBUG_MODE = False  # Set to True to enable debug logging

def configure_logging(debug_mode: bool = DEBUG_MODE):
    """Configure logging based on debug mode"""
    global DEBUG_MODE
    DEBUG_MODE = debug_mode
    
    if DEBUG_MODE:
        log_level = logging.DEBUG
        format_str = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    else:
        log_level = logging.WARNING
        format_str = '%(levelname)s - %(message)s'

    # Remove all handlers
    for handler in logging.root.handlers[:]:
        logging.root.removeHandler(handler)

    # Configure root logger
    logging.basicConfig(
        level=log_level,
        format=format_str,
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('bot.log', encoding='utf-8')
        ]
    )

    # Set level for specific loggers
    logging.getLogger('checkerbot').setLevel(log_level)
    logging.getLogger('check_group_order').setLevel(log_level)
    logging.getLogger('discord').setLevel(logging.WARNING)  # Always keep Discord at WARNING