# The Method Discord Bots

This repository contains several Discord bots used by The Method for order management and processing.

## Project Structure

The project is organized into separate folders for each bot:

- `themethodbot/` - The main Discord bot for order management
- `budgetbot/` - Bo<PERSON> for budget management
- `eatscheckerbot/` - <PERSON><PERSON> for checking Uber Eats orders
- `common/` - Shared code used by all bots

## Running the Bots

### Running All Bots at Once

You can run all bots simultaneously with a single command:

```bash
# Run all bots at once
python main.py all
```

Or simply double-click the `run_all_bots.bat` file (Windows) or run `./run_all_bots.sh` (Linux/Mac).

### Running Individual Bots

You can run each bot individually using the main.py script:

```bash
# Run The Method Bot
python main.py themethodbot

# Run Budget Bot
python main.py budgetbot

# Run Eats Checker Bot
python main.py eatscheckerbot

# Run Checker Bot
python main.py checkerbot
```

Alternatively, you can run each bot directly from its folder:

```bash
# Run The Method Bot
cd themethodbot
python main.py

# Run Budget Bot
cd budgetbot
python main.py

# Run Eats Checker Bot
cd eatscheckerbot
python main.py

# Run Checker Bot
cd eatscheckerbot
python checkerbot_main.py
```

## Dependencies

All bots share common dependencies which are listed in the requirements.txt file.

## Configuration

Each bot uses environment variables for configuration. Make sure to set up the appropriate .env file with the required variables.

## Common Code

The `common/` directory contains shared code used by all bots:

- `bot.py` - Common bot functionality
- `check_group_order.py` - Functions for checking Uber Eats group orders
- `extract_label.py` - Functions for extracting labels from orders
- `config.py` - Shared configuration
