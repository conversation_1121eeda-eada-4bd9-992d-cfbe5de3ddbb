import discord
import logging
from typing import List, Dict, Any, Optional

logger = logging.getLogger('themethodbot.embeds')

def create_locked_order_embed():
    """Create a modern embed for locked/canceled orders."""
    embed = discord.Embed(
        title="🔒 Group Order is Locked",
        description="This group order appears to be locked or canceled.",
        color=discord.Color.from_rgb(237, 66, 69)  # Discord Red
    )

    # Add a clean divider
    embed.add_field(
        name="",
        value="━━━━━━━━━━━━━━━━━━━━━━━━━",
        inline=False
    )

    # Add steps with better formatting
    embed.add_field(
        name="🔸 Step 1",
        value="Go back to your Uber Eats Cart",
        inline=False
    )

    embed.add_field(
        name="🔹 Step 2",
        value="Press **Unlock Group Order**",
        inline=False
    )

    embed.add_field(
        name="🔸 Step 3",
        value="Resend your cart link to this channel",
        inline=False
    )

    # Set a nice thumbnail
    embed.set_thumbnail(url="https://i.imgur.com/JqA9FeL.png")  # Lock icon

    # Add footer with helpful tip
    embed.set_footer(text="If you continue to have issues, please contact support")

    return embed

def create_non_promo_embed():
    """Create a modern embed for stores not in promo."""
    embed = discord.Embed(
        title="🔎 Find Eligible Stores",
        description="**The store you're looking for isn't in our promo. Follow these steps to find eligible restaurants:**",
        color=discord.Color.from_rgb(254, 231, 92)  # Bright Yellow
    )

    # Add a clean divider
    embed.add_field(
        name="",
        value="━━━━━━━━━━━━━━━━━━━━━━━━━",
        inline=False
    )

    # Step 1
    embed.add_field(
        name="🔸 Step 1: Open Our Special Link",
        value="Open [**this link**](https://tinyurl.com/TheMethodUE) in Chrome or Safari",
        inline=False
    )

    # Step 2
    embed.add_field(
        name="🔹 Step 2: Enter Your Address",
        value="Log in to Uber Eats and enter your delivery address",
        inline=False
    )

    # Step 3
    embed.add_field(
        name="🔸 Step 3: Look for the Badge",
        value="Look for restaurants with the **30% OFF** badge",
        inline=False
    )

    # Set a thumbnail instead of full image for cleaner look
    embed.set_thumbnail(url="https://i.imgur.com/JNRmHjM.png")  # Search/store icon

    # Add a footer with a tip
    embed.set_footer(text="Tip: If you still don't see eligible stores, try clearing your browser cookies")

    # Add the screenshot as a smaller image at the bottom
    embed.set_image(url="https://media.discordapp.net/attachments/978162420423999498/1349998616110043237/image.png?width=600&height=338")

    return embed

def create_order_summary_embed(result: Dict[str, Any], cart_items: List[str], fee_calculations: Dict[str, Any]) -> discord.Embed:
    """Create a modern order summary embed with enhanced visuals."""
    embed = discord.Embed(
        title="🍽️ The Method Order Summary",
        color=discord.Color.from_rgb(87, 242, 135)  # Vibrant Green
    )

    # Add group order link if available with better formatting
    group_link = result.get('group_link', 'Not available')

    # Check if the store is in promo
    is_promo = result.get('is_promo', True)
    promo_status = "✅ Store is in promo!" if is_promo else "❌ Store is not in promo"

    embed.description = f"**🎟️ Promo Status:** {promo_status}\n**🔗 [Group Order Link]({group_link})**\n\n"

    # Add a clean divider
    embed.add_field(
        name="",
        value="━━━━━━━━━━━━━━━━━━━━━━━━━",
        inline=False
    )

    # Location with better formatting
    location = result.get('location', {})
    location_str = f"{location.get('address', 'Unknown Address')}"
    if location.get('city'):
        location_str += f", {location.get('city')}"
    if location.get('state'):
        location_str += f", {location.get('state').upper()}"
    if location.get('zipcode'):
        location_str += f" {location.get('zipcode')}"

    embed.add_field(
        name="📍 Delivery Location",
        value=location_str,
        inline=False
    )

    # Add store URL if available
    store_url = result.get('store_url', '')
    if store_url:
        embed.add_field(
            name="🏪 Restaurant",
            value=f"[View on Uber Eats]({store_url})",
            inline=False
        )

    # Add cart items with better formatting
    if cart_items:
        # Limit to 10 items to avoid embed being too large
        items_to_show = cart_items[:10]
        remaining_items = len(cart_items) - 10 if len(cart_items) > 10 else 0

        items_str = "\\n".join([f"• {item}" for item in items_to_show])
        if remaining_items > 0:
            items_str += f"\\n• ... and {remaining_items} more items"

        embed.add_field(
            name=f"🛒 Order Items ({len(cart_items)})",
            value=items_str,
            inline=False
        )

    # Price breakdown with better formatting
    currency = "CA$" if fee_calculations.get('is_cad', False) else "$"

    # Calculate new pricing structure
    original_subtotal = fee_calculations.get('subtotal', 0)

    # Special case for $24 subtotal
    if original_subtotal == 24.00:
        discounted_subtotal = 0.00
        discount_amount = original_subtotal
    else:
        # Fixed $25 discount
        discount_amount = 25.00
        discounted_subtotal = max(0, original_subtotal - discount_amount)

    savings = original_subtotal - discounted_subtotal

    # Original vs. discounted price
    embed.add_field(
        name="💰 Price Breakdown",
        value=f"**Original Subtotal:** `{currency}{original_subtotal:.2f}`\n" +
              f"**Discount:** `-{currency}{discount_amount:.2f}`\n" +
              f"**Discounted Subtotal:** `{currency}{discounted_subtotal:.2f}`\n" +
              f"**You Save:** `{currency}{savings:.2f}`",
        inline=False
    )

    # Fees breakdown with fixed $10 service fee
    fees_str = ""

    if fee_calculations.get('overflow_fee', 0) > 0:
        fees_str += f"**Overflow Fee:** `{currency}{fee_calculations.get('overflow_fee', 0):.2f}`\n"

    # Fixed $10 service fee
    fees_str += f"**Service Fee:** `{currency}10.00`\n"

    if fee_calculations.get('ca_driver_benefit', 0) > 0:
        fees_str += f"**Driver Benefit:** `{currency}{fee_calculations.get('ca_driver_benefit', 0):.2f}`\n"

    fees_str += f"**Taxes:** `{currency}{fee_calculations.get('taxes', 0):.2f}`"

    embed.add_field(
        name="💸 Fees & Taxes",
        value=fees_str,
        inline=False
    )

    # Calculate new final total
    total_fees = 10.00  # Fixed service fee
    if fee_calculations.get('overflow_fee', 0) > 0:
        total_fees += fee_calculations.get('overflow_fee', 0)
    if fee_calculations.get('ca_driver_benefit', 0) > 0:
        total_fees += fee_calculations.get('ca_driver_benefit', 0)
    total_fees += fee_calculations.get('taxes', 0)

    final_total = discounted_subtotal + total_fees

    # Final total with emphasis
    embed.add_field(
        name="💲 Estimated Final Total",
        value=f"**`{currency}{final_total:.2f}`**",
        inline=False
    )

    # Set a nice thumbnail
    embed.set_thumbnail(url="https://i.imgur.com/GwSvqWD.png")  # Food/order icon

    # Add footer with helpful information
    embed.set_footer(text="A Chef or Waiter will assist you shortly | The Method")

    return embed

def create_sv_payment_embed() -> discord.Embed:
    """Create a simplified payment details embed for sv."""
    embed = discord.Embed(
        title="💳 sv's Payment Methods",
        color=discord.Color.from_rgb(88, 101, 242)  # Discord Blurple
    )

    # Payment options for sv
    embed.add_field(
        name="💵 Payment Options",
        value="**Venmo:** @svarde\n**PayPal:** <EMAIL>\n**Cash App:** $ezglory ⚠️ *(Restricted)*",
        inline=False
    )

    # Set a nice thumbnail
    embed.set_thumbnail(url="https://images-ext-1.discordapp.net/external/bRiKpP4Tw6Q1jqWW7nNDCU5y4zWcfyEeo0ooW5uFxz4/https/i.gyazo.com/e1abb32a09bb93d21e7e342cfdc44a1e.gif?width=72&height=72")

    # Add footer
    embed.set_footer(text="sv's Payment Methods | The Method")

    return embed

def check_order_limits(subtotal: float, is_cad: bool) -> Optional[discord.Embed]:
    """Check if order meets minimum/maximum requirements with modern embed."""
    currency = "CA$" if is_cad else "$"
    min_order = 30.0 if is_cad else 24.0
    max_order = 35.0 if is_cad else 30.0

    if subtotal < min_order:
        embed = discord.Embed(
            title="⚠️ Minimum Order Requirement",
            description=f"Your subtotal must be at least `{currency}{min_order:.2f}` before fees and taxes.",
            color=discord.Color.from_rgb(254, 231, 92)  # Bright Yellow
        )

        # Add a clean divider
        embed.add_field(
            name="",
            value="━━━━━━━━━━━━━━━━━━━━━━━━━",
            inline=False
        )

        # Add action required
        embed.add_field(
            name="👉 Action Required",
            value="Please add more items to your cart to meet the minimum requirement.",
            inline=False
        )

        # Add current vs required
        embed.add_field(
            name="📊 Current vs Required",
            value=f"**Current Subtotal:** `{currency}{subtotal:.2f}`\n" +
                  f"**Minimum Required:** `{currency}{min_order:.2f}`\n" +
                  f"**Amount to Add:** `{currency}{(min_order - subtotal):.2f}`",
            inline=False
        )

        # Set a nice thumbnail
        embed.set_thumbnail(url="https://i.imgur.com/YKI4LbC.png")  # Warning icon

        return embed

    elif subtotal > max_order:
        embed = discord.Embed(
            title="⚠️ Maximum Order Limit",
            description=f"Your subtotal must be at most `{currency}{max_order:.2f}` before fees and taxes.",
            color=discord.Color.from_rgb(254, 231, 92)  # Bright Yellow
        )

        # Add a clean divider
        embed.add_field(
            name="",
            value="━━━━━━━━━━━━━━━━━━━━━━━━━",
            inline=False
        )

        # Add action required
        embed.add_field(
            name="👉 Action Required",
            value="Please remove some items from your cart to meet the maximum limit.",
            inline=False
        )

        # Add current vs required
        embed.add_field(
            name="📊 Current vs Limit",
            value=f"**Current Subtotal:** `{currency}{subtotal:.2f}`\n" +
                  f"**Maximum Allowed:** `{currency}{max_order:.2f}`\n" +
                  f"**Amount to Remove:** `{currency}{(subtotal - max_order):.2f}`",
            inline=False
        )

        # Set a nice thumbnail
        embed.set_thumbnail(url="https://i.imgur.com/YKI4LbC.png")  # Warning icon

        return embed

    return None

def create_error_embed(error_message: str) -> discord.Embed:
    """Create a modern error embed."""
    embed = discord.Embed(
        title="❌ Error Processing Order",
        description=f"We encountered an error while processing your order:\n\n```{error_message}```",
        color=discord.Color.from_rgb(237, 66, 69)  # Discord Red
    )

    # Add a clean divider
    embed.add_field(
        name="",
        value="━━━━━━━━━━━━━━━━━━━━━━━━━",
        inline=False
    )

    # Add troubleshooting steps
    embed.add_field(
        name="🔍 Troubleshooting Steps",
        value="1. Make sure your group order is unlocked\n" +
              "2. Try creating a new group order\n" +
              "3. Check that you're using a supported restaurant",
        inline=False
    )

    # Set a nice thumbnail
    embed.set_thumbnail(url="https://i.imgur.com/YKI4LbC.png")  # Warning icon

    # Add footer with support info
    embed.set_footer(text="If the issue persists, please contact support")

    return embed

def create_processing_embed() -> discord.Embed:
    """Create a modern processing embed."""
    embed = discord.Embed(
        title="🔄 Processing Your Order",
        description="We're analyzing your Uber Eats group order. This may take a few seconds...",
        color=discord.Color.from_rgb(88, 101, 242)  # Discord Blurple
    )

    # Set a nice thumbnail
    embed.set_thumbnail(url="https://i.imgur.com/3gL0FzN.gif")  # Loading animation

    return embed
